/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"



void setup_scr_test1(lv_ui *ui)
{
    //Write codes test1
    ui->test1 = lv_obj_create(NULL);
    lv_obj_set_size(ui->test1, 480, 800);
    lv_obj_set_scrollbar_mode(ui->test1, LV_SCROLLBAR_MODE_OFF);

    //Write style for test1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->test1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes test1_img_1
    ui->test1_img_1 = lv_image_create(ui->test1);
    lv_obj_set_pos(ui->test1_img_1, 0, 0);
    lv_obj_set_size(ui->test1_img_1, 480, 800);
    lv_obj_add_flag(ui->test1_img_1, LV_OBJ_FLAG_CLICKABLE);
    lv_image_set_src(ui->test1_img_1, &_test1_RGB565_480x800);
    lv_image_set_pivot(ui->test1_img_1, 50,50);
    lv_image_set_rotation(ui->test1_img_1, 0);

    //Write style for test1_img_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_image_recolor_opa(ui->test1_img_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_image_opa(ui->test1_img_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);

    //The custom code of test1.


    //Update current screen layout.
    lv_obj_update_layout(ui->test1);

}
