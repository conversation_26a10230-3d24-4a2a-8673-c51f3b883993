<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [LCD_demo\LCD_demo.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image LCD_demo\LCD_demo.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Mon Sep 15 16:44:05 2025
<BR><P>
<H3>Maximum Stack Usage =        904 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
lv_image_event &rArr; lv_image_set_src &rArr; update_align &rArr; lv_image_set_pivot &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1c]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC_IRQHandler</a><BR>
 <LI><a href="#[4]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">BusFault_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">MemManage_Handler</a><BR>
 <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[5]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">UsageFault_Handler</a><BR>
 <LI><a href="#[125]">event_send_core</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[125]">event_send_core</a><BR>
 <LI><a href="#[164]">lv_area_is_point_on</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[164]">lv_area_is_point_on</a><BR>
 <LI><a href="#[1f8]">lv_obj_transform_point_array</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1f8]">lv_obj_transform_point_array</a><BR>
 <LI><a href="#[1fb]">lv_obj_move_children_by</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1fb]">lv_obj_move_children_by</a><BR>
 <LI><a href="#[152]">layout_update_core</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[152]">layout_update_core</a><BR>
 <LI><a href="#[204]">refresh_children_style</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[204]">refresh_children_style</a><BR>
 <LI><a href="#[1d6]">lv_obj_construct</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1d6]">lv_obj_construct</a><BR>
 <LI><a href="#[c4]">apply_theme_recursion</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[c4]">apply_theme_recursion</a><BR>
 <LI><a href="#[c3]">apply_theme</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[c3]">apply_theme</a><BR>
 <LI><a href="#[1de]">obj_delete_core</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1de]">obj_delete_core</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1c]">ADC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1e]">CAN1_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1f]">CAN1_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[20]">CAN1_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1d]">CAN1_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4a]">CAN2_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4b]">CAN2_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4c]">CAN2_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[49]">CAN2_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[58]">DCMI_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[15]">DMA1_Stream0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[16]">DMA1_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[17]">DMA1_Stream2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[18]">DMA1_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[39]">DMA1_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[63]">DMA2D_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[42]">DMA2_Stream0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[43]">DMA2_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[44]">DMA2_Stream2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[45]">DMA2_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4e]">DMA2_Stream5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4f]">DMA2_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[50]">DMA2_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[47]">ETH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[48]">ETH_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[32]">EXTI15_10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[21]">EXTI9_5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[e]">FLASH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3a]">FMC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5a]">FPU_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[59]">HASH_RNG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2a]">I2C1_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[29]">I2C1_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2c]">I2C2_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2b]">I2C2_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[53]">I2C3_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[52]">I2C3_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[62]">LTDC_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[61]">LTDC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4d]">OTG_FS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[34]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[55]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[54]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[57]">OTG_HS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[56]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[b]">PVD_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from stm32f4xx_it.o(i.PendSV_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[f]">RCC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[33]">RTC_Alarm_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[d]">RTC_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[60]">SAI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3b]">SDIO_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2d]">SPI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2e]">SPI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3d]">SPI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5d]">SPI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5e]">SPI5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5f]">SPI6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from stm32f4xx_it.o(i.SysTick_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[65]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[c]">TAMP_STAMP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[22]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[25]">TIM1_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[24]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[23]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[26]">TIM2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[27]">TIM3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[28]">TIM4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3c]">TIM5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[40]">TIM6_DAC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[41]">TIM7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[35]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[38]">TIM8_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[37]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[36]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3e]">UART4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3f]">UART5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5b]">UART7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5c]">UART8_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2f]">USART1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[30]">USART2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[31]">USART3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[51]">USART6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[a]">WWDG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[66]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[68]">kern_pair_16_compare</a> from lv_font_fmt_txt.o(i.kern_pair_16_compare) referenced from lv_font_fmt_txt.o(i.get_kern_value)
 <LI><a href="#[69]">kern_pair_8_compare</a> from lv_font_fmt_txt.o(i.kern_pair_8_compare) referenced from lv_font_fmt_txt.o(i.get_kern_value)
 <LI><a href="#[72]">lv_anim_path_ease_out</a> from lv_anim.o(i.lv_anim_path_ease_out) referenced from lv_obj_scroll.o(i.lv_obj_scroll_by)
 <LI><a href="#[6a]">lv_anim_path_linear</a> from lv_anim.o(i.lv_anim_path_linear) referenced from lv_anim.o(i.lv_anim_init)
 <LI><a href="#[6b]">lv_async_timer_cb</a> from lv_async.o(i.lv_async_timer_cb) referenced from lv_async.o(i.lv_async_call_cancel)
 <LI><a href="#[81]">lv_font_get_bitmap_fmt_txt</a> from lv_font_fmt_txt.o(i.lv_font_get_bitmap_fmt_txt) referenced from lv_font_montserrat_14.o(.constdata)
 <LI><a href="#[80]">lv_font_get_glyph_dsc_fmt_txt</a> from lv_font_fmt_txt.o(i.lv_font_get_glyph_dsc_fmt_txt) referenced from lv_font_montserrat_14.o(.constdata)
 <LI><a href="#[85]">lv_image_constructor</a> from lv_image.o(i.lv_image_constructor) referenced from lv_image.o(.constdata)
 <LI><a href="#[86]">lv_image_destructor</a> from lv_image.o(i.lv_image_destructor) referenced from lv_image.o(.constdata)
 <LI><a href="#[87]">lv_image_event</a> from lv_image.o(i.lv_image_event) referenced from lv_image.o(.constdata)
 <LI><a href="#[6d]">lv_ll_get_head</a> from lv_ll.o(i.lv_ll_get_head) referenced from lv_group.o(i.lv_group_focus_next)
 <LI><a href="#[6c]">lv_ll_get_next</a> from lv_ll.o(i.lv_ll_get_next) referenced from lv_group.o(i.lv_group_focus_next)
 <LI><a href="#[6e]">lv_ll_get_prev</a> from lv_ll.o(i.lv_ll_get_prev) referenced from lv_group.o(i.lv_group_focus_prev)
 <LI><a href="#[6f]">lv_ll_get_tail</a> from lv_ll.o(i.lv_ll_get_tail) referenced from lv_group.o(i.lv_group_focus_prev)
 <LI><a href="#[7d]">lv_obj_constructor</a> from lv_obj.o(i.lv_obj_constructor) referenced from lv_obj.o(.constdata)
 <LI><a href="#[7c]">lv_obj_delete_async_cb</a> from lv_obj_tree.o(i.lv_obj_delete_async_cb) referenced from lv_obj_tree.o(i.obj_delete_core)
 <LI><a href="#[7e]">lv_obj_destructor</a> from lv_obj.o(i.lv_obj_destructor) referenced from lv_obj.o(.constdata)
 <LI><a href="#[7f]">lv_obj_event</a> from lv_obj.o(i.lv_obj_event) referenced from lv_obj.o(.constdata)
 <LI><a href="#[84]">lv_text_utf8_get_byte_id</a> from lv_text.o(i.lv_text_utf8_get_byte_id) referenced from lv_text.o(.constdata)
 <LI><a href="#[83]">lv_text_utf8_next</a> from lv_text.o(i.lv_text_utf8_next) referenced from lv_text.o(.constdata)
 <LI><a href="#[82]">lv_text_utf8_size</a> from lv_text.o(i.lv_text_utf8_size) referenced from lv_text.o(.constdata)
 <LI><a href="#[64]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[7b]">opa_scale_anim</a> from lv_display.o(i.opa_scale_anim) referenced from lv_display.o(i.lv_screen_load_anim)
 <LI><a href="#[78]">scr_anim_completed</a> from lv_display.o(i.scr_anim_completed) referenced from lv_display.o(i.lv_screen_load_anim)
 <LI><a href="#[77]">scr_load_anim_start</a> from lv_display.o(i.scr_load_anim_start) referenced from lv_display.o(i.lv_screen_load_anim)
 <LI><a href="#[70]">scroll_end_cb</a> from lv_obj_scroll.o(i.scroll_end_cb) referenced from lv_obj_scroll.o(i.lv_obj_scroll_by)
 <LI><a href="#[71]">scroll_x_anim</a> from lv_obj_scroll.o(i.scroll_x_anim) referenced from lv_obj_scroll.o(i.lv_obj_scroll_by)
 <LI><a href="#[71]">scroll_x_anim</a> from lv_obj_scroll.o(i.scroll_x_anim) referenced from lv_obj_scroll.o(i.lv_obj_scroll_to_x)
 <LI><a href="#[71]">scroll_x_anim</a> from lv_obj_scroll.o(i.scroll_x_anim) referenced from lv_obj_scroll.o(i.scroll_area_into_view)
 <LI><a href="#[73]">scroll_y_anim</a> from lv_obj_scroll.o(i.scroll_y_anim) referenced from lv_obj_scroll.o(i.lv_obj_scroll_by)
 <LI><a href="#[73]">scroll_y_anim</a> from lv_obj_scroll.o(i.scroll_y_anim) referenced from lv_obj_scroll.o(i.lv_obj_scroll_to_y)
 <LI><a href="#[73]">scroll_y_anim</a> from lv_obj_scroll.o(i.scroll_y_anim) referenced from lv_obj_scroll.o(i.scroll_area_into_view)
 <LI><a href="#[79]">set_x_anim</a> from lv_display.o(i.set_x_anim) referenced from lv_display.o(i.lv_screen_load_anim)
 <LI><a href="#[7a]">set_y_anim</a> from lv_display.o(i.set_y_anim) referenced from lv_display.o(i.lv_screen_load_anim)
 <LI><a href="#[74]">trans_anim_cb</a> from lv_obj_style.o(i.trans_anim_cb) referenced from lv_obj_style.o(i.lv_obj_style_create_transition)
 <LI><a href="#[76]">trans_anim_completed_cb</a> from lv_obj_style.o(i.trans_anim_completed_cb) referenced from lv_obj_style.o(i.lv_obj_style_create_transition)
 <LI><a href="#[75]">trans_anim_start_cb</a> from lv_obj_style.o(i.trans_anim_start_cb) referenced from lv_obj_style.o(i.lv_obj_style_create_transition)
 <LI><a href="#[67]">unicode_list_compare</a> from lv_font_fmt_txt.o(i.unicode_list_compare) referenced from lv_font_fmt_txt.o(i.get_glyph_dsc_id)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[66]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[256]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[88]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[8e]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[257]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[258]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[259]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[25a]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[25b]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[25c]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>

<P><STRONG><a name="[8d]"></a>__aeabi_ldivmod</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, ldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cubic_bezier
</UL>

<P><STRONG><a name="[8c]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[25d]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[8b]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[25e]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[89]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[25f]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[a1]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_MspInit
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_LTDC_Init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FMC_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA2D_Init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[aa]"></a>FMC_SDRAM_Init</STRONG> (Thumb, 116 bytes, Stack size 12 bytes, stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = FMC_SDRAM_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SDRAM_Init
</UL>

<P><STRONG><a name="[ad]"></a>FMC_SDRAM_ProgramRefreshRate</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_ll_fmc.o(i.FMC_SDRAM_ProgramRefreshRate))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SDRAM_ProgramRefreshRate
</UL>

<P><STRONG><a name="[8f]"></a>FMC_SDRAM_SendCommand</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FMC_SDRAM_SendCommand
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SDRAM_SendCommand
</UL>

<P><STRONG><a name="[ab]"></a>FMC_SDRAM_Timing_Init</STRONG> (Thumb, 146 bytes, Stack size 4 bytes, stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Timing_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = FMC_SDRAM_Timing_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SDRAM_Init
</UL>

<P><STRONG><a name="[91]"></a>HAL_DMA2D_Init</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, stm32f4xx_hal_dma2d.o(i.HAL_DMA2D_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_DMA2D_Init &rArr; HAL_DMA2D_MspInit
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA2D_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA2D_Init
</UL>

<P><STRONG><a name="[92]"></a>HAL_DMA2D_MspInit</STRONG> (Thumb, 36 bytes, Stack size 4 bytes, dma2d.o(i.HAL_DMA2D_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_DMA2D_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA2D_Init
</UL>

<P><STRONG><a name="[93]"></a>HAL_Delay</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDRAM_Initialization_Sequence
</UL>

<P><STRONG><a name="[95]"></a>HAL_GPIO_Init</STRONG> (Thumb, 564 bytes, Stack size 40 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_MspInit
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FMC_MspInit
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[b6]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_LTDC_Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[90]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_EnableOverDrive
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FMC_SDRAM_SendCommand
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>

<P><STRONG><a name="[bc]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[96]"></a>HAL_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32f4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[98]"></a>HAL_InitTick</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[9c]"></a>HAL_LTDC_ConfigLayer</STRONG> (Thumb, 124 bytes, Stack size 40 bytes, stm32f4xx_hal_ltdc.o(i.HAL_LTDC_ConfigLayer))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_LTDC_ConfigLayer &rArr; LTDC_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_LTDC_Init
</UL>

<P><STRONG><a name="[9e]"></a>HAL_LTDC_Init</STRONG> (Thumb, 192 bytes, Stack size 16 bytes, stm32f4xx_hal_ltdc.o(i.HAL_LTDC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = HAL_LTDC_Init &rArr; HAL_LTDC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_LTDC_Init
</UL>

<P><STRONG><a name="[9f]"></a>HAL_LTDC_MspInit</STRONG> (Thumb, 254 bytes, Stack size 96 bytes, ltdc.o(i.HAL_LTDC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = HAL_LTDC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_Init
</UL>

<P><STRONG><a name="[a2]"></a>HAL_LTDC_SetWindowPosition</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetWindowPosition))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_LTDC_SetWindowPosition &rArr; LTDC_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_Layer_Window_Config
</UL>

<P><STRONG><a name="[a3]"></a>HAL_LTDC_SetWindowSize</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_hal_ltdc.o(i.HAL_LTDC_SetWindowSize))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_LTDC_SetWindowSize &rArr; LTDC_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_Layer_Window_Config
</UL>

<P><STRONG><a name="[99]"></a>HAL_MspInit</STRONG> (Thumb, 42 bytes, Stack size 4 bytes, stm32f4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[9b]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 98 bytes, Stack size 4 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[97]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[a4]"></a>HAL_PWREx_EnableOverDrive</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_PWREx_EnableOverDrive
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[a0]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 638 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_MspInit
</UL>

<P><STRONG><a name="[a5]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 354 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[a6]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[a7]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1154 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[a8]"></a>HAL_SDRAM_Init</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, stm32f4xx_hal_sdram.o(i.HAL_SDRAM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_SDRAM_Init &rArr; HAL_SDRAM_MspInit &rArr; HAL_FMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SDRAM_MspInit
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FMC_SDRAM_Timing_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FMC_SDRAM_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FMC_Init
</UL>

<P><STRONG><a name="[a9]"></a>HAL_SDRAM_MspInit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, fmc.o(i.HAL_SDRAM_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SDRAM_MspInit &rArr; HAL_FMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FMC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SDRAM_Init
</UL>

<P><STRONG><a name="[ac]"></a>HAL_SDRAM_ProgramRefreshRate</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, stm32f4xx_hal_sdram.o(i.HAL_SDRAM_ProgramRefreshRate))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SDRAM_ProgramRefreshRate
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FMC_SDRAM_ProgramRefreshRate
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDRAM_Initialization_Sequence
</UL>

<P><STRONG><a name="[ae]"></a>HAL_SDRAM_SendCommand</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, stm32f4xx_hal_sdram.o(i.HAL_SDRAM_SendCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_SDRAM_SendCommand &rArr; FMC_SDRAM_SendCommand
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FMC_SDRAM_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDRAM_Send_Cmd
</UL>

<P><STRONG><a name="[9a]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[af]"></a>LTDC_Clear</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, my_ltdc.o(i.LTDC_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LTDC_Clear &rArr; LTDC_Fill
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_Fill
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_LTDC_Init
</UL>

<P><STRONG><a name="[b9]"></a>LTDC_Display_Dir</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, my_ltdc.o(i.LTDC_Display_Dir))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_LTDC_Init
</UL>

<P><STRONG><a name="[b0]"></a>LTDC_Fill</STRONG> (Thumb, 172 bytes, Stack size 28 bytes, my_ltdc.o(i.LTDC_Fill))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = LTDC_Fill
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_Clear
</UL>

<P><STRONG><a name="[b1]"></a>LTDC_Layer_Window_Config</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, my_ltdc.o(i.LTDC_Layer_Window_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = LTDC_Layer_Window_Config &rArr; HAL_LTDC_SetWindowSize &rArr; LTDC_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_SetWindowSize
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_SetWindowPosition
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_LTDC_Init
</UL>

<P><STRONG><a name="[ba]"></a>LTDC_Select_Layer</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, my_ltdc.o(i.LTDC_Select_Layer))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_LTDC_Init
</UL>

<P><STRONG><a name="[b8]"></a>LTDC_setconfig</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, my_ltdc.o(i.LTDC_setconfig))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_LTDC_Init
</UL>

<P><STRONG><a name="[b2]"></a>MX_DMA2D_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, dma2d.o(i.MX_DMA2D_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = MX_DMA2D_Init &rArr; HAL_DMA2D_Init &rArr; HAL_DMA2D_MspInit
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA2D_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b3]"></a>MX_FMC_Init</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, fmc.o(i.MX_FMC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = MX_FMC_Init &rArr; HAL_SDRAM_Init &rArr; HAL_SDRAM_MspInit &rArr; HAL_FMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDRAM_Initialization_Sequence
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SDRAM_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b5]"></a>MX_GPIO_Init</STRONG> (Thumb, 198 bytes, Stack size 40 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b7]"></a>MX_LTDC_Init</STRONG> (Thumb, 232 bytes, Stack size 72 bytes, ltdc.o(i.MX_LTDC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = MX_LTDC_Init &rArr; HAL_LTDC_Init &rArr; HAL_LTDC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_setconfig
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_Select_Layer
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_Layer_Window_Config
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_Display_Dir
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LTDC_Clear
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_ConfigLayer
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[b4]"></a>SDRAM_Initialization_Sequence</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, my_sdram.o(i.SDRAM_Initialization_Sequence))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SDRAM_Initialization_Sequence &rArr; SDRAM_Send_Cmd &rArr; HAL_SDRAM_SendCommand &rArr; FMC_SDRAM_SendCommand
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SDRAM_ProgramRefreshRate
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDRAM_Send_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FMC_Init
</UL>

<P><STRONG><a name="[bb]"></a>SDRAM_Send_Cmd</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, my_sdram.o(i.SDRAM_Send_Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SDRAM_Send_Cmd &rArr; HAL_SDRAM_SendCommand &rArr; FMC_SDRAM_SendCommand
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SDRAM_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDRAM_Initialization_Sequence
</UL>

<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[bd]"></a>SystemClock_Config</STRONG> (Thumb, 150 bytes, Stack size 88 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_EnableOverDrive
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[65]"></a>SystemInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system_stm32f4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[260]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[261]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[262]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[254]"></a>init_keyboard</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gui_guider.o(i.init_keyboard))
<BR><BR>[Called By]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_ui
</UL>

<P><STRONG><a name="[253]"></a>init_scr_del_flag</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gui_guider.o(i.init_scr_del_flag))
<BR><BR>[Called By]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_ui
</UL>

<P><STRONG><a name="[157]"></a>lv_anim_delete</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, lv_anim.o(i.lv_anim_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = lv_anim_delete &rArr; remove_anim &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_next
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_anim
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anim_mark_list_change
</UL>
<BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_y
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_x
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_destructor
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_delete
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[159]"></a>lv_anim_init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, lv_anim.o(i.lv_anim_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_anim_init
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[72]"></a>lv_anim_path_ease_out</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lv_anim.o(i.lv_anim_path_ease_out))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = lv_anim_path_ease_out &rArr; lv_anim_path_cubic_bezier &rArr; lv_cubic_bezier &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_path_cubic_bezier
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_obj_scroll.o(i.lv_obj_scroll_by)
</UL>
<P><STRONG><a name="[6a]"></a>lv_anim_path_linear</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, lv_anim.o(i.lv_anim_path_linear))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lv_anim_path_linear
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_map
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_anim.o(i.lv_anim_init)
</UL>
<P><STRONG><a name="[24e]"></a>lv_anim_resolve_speed</STRONG> (Thumb, 94 bytes, Stack size 4 bytes, lv_anim.o(i.lv_anim_resolve_speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_anim_resolve_speed
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resolve_time
</UL>

<P><STRONG><a name="[22b]"></a>lv_anim_set_completed_cb</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_set_completed_cb))
<BR><BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
</UL>

<P><STRONG><a name="[22c]"></a>lv_anim_set_delay</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_set_delay))
<BR><BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
</UL>

<P><STRONG><a name="[20d]"></a>lv_anim_set_deleted_cb</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_set_deleted_cb))
<BR><BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[20f]"></a>lv_anim_set_duration</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_set_duration))
<BR><BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[22d]"></a>lv_anim_set_early_apply</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_set_early_apply))
<BR><BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
</UL>

<P><STRONG><a name="[211]"></a>lv_anim_set_exec_cb</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_set_exec_cb))
<BR><BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[212]"></a>lv_anim_set_path_cb</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_set_path_cb))
<BR><BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[22a]"></a>lv_anim_set_start_cb</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_set_start_cb))
<BR><BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
</UL>

<P><STRONG><a name="[22e]"></a>lv_anim_set_user_data</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_set_user_data))
<BR><BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
</UL>

<P><STRONG><a name="[210]"></a>lv_anim_set_values</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_set_values))
<BR><BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[20c]"></a>lv_anim_set_var</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_set_var))
<BR><BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[20e]"></a>lv_anim_speed_clamped</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, lv_anim.o(i.lv_anim_speed_clamped))
<BR><BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[15d]"></a>lv_anim_start</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, lv_anim.o(i.lv_anim_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = lv_anim_start &rArr; lv_ll_ins_head &rArr; lv_malloc &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_ins_head
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memcpy
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tick_get
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resolve_time
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_concurrent_anims
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anim_mark_list_change
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[116]"></a>lv_area_align</STRONG> (Thumb, 688 bytes, Stack size 20 bytes, lv_area.o(i.lv_area_align))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = lv_area_align
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_width
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_height
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[f3]"></a>lv_area_get_height</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_area.o(i.lv_area_get_height))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_image_dsc
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_point_on
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;transform_point_array
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_height
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_align
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_task
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[11f]"></a>lv_area_get_size</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_area.o(i.lv_area_get_size))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_scrollbar
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scrollbar_invalidate
</UL>

<P><STRONG><a name="[f9]"></a>lv_area_get_width</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_area.o(i.lv_area_get_width))
<BR><BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_image_dsc
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_point_on
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;transform_point_array
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_width
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_align
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[193]"></a>lv_area_increase</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, lv_area.o(i.lv_area_increase))
<BR><BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_click_area
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
</UL>

<P><STRONG><a name="[11a]"></a>lv_area_intersect</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, lv_area.o(i.lv_area_intersect))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_area_intersect
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_inv_area
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[113]"></a>lv_area_is_in</STRONG> (Thumb, 184 bytes, Stack size 24 bytes, lv_area.o(i.lv_area_is_in))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = lv_area_is_in &rArr; lv_area_is_point_on &rArr;  lv_area_is_point_on (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_point_on
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_point_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_inv_area
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[164]"></a>lv_area_is_point_on</STRONG> (Thumb, 296 bytes, Stack size 32 bytes, lv_area.o(i.lv_area_is_point_on))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + In Cycle
<LI>Call Chain = lv_area_is_point_on &rArr;  lv_area_is_point_on (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_width
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_height
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_point_on
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_point_within_circle
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_in
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_point_on
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
</UL>

<P><STRONG><a name="[119]"></a>lv_area_move</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, lv_area.o(i.lv_area_move))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[106]"></a>lv_area_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lv_area.o(i.lv_area_set))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_buf_get_full_area
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[fd]"></a>lv_array_at</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, lv_array.o(i.lv_array_at))
<BR><BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_array_at
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_event_list_core
</UL>

<P><STRONG><a name="[101]"></a>lv_array_deinit</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, lv_array.o(i.lv_array_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = lv_array_deinit &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_event_list_core
</UL>

<P><STRONG><a name="[100]"></a>lv_array_resize</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, lv_array.o(i.lv_array_resize))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_event_list_core
</UL>

<P><STRONG><a name="[166]"></a>lv_async_call_cancel</STRONG> (Thumb, 78 bytes, Stack size 32 bytes, lv_async.o(i.lv_async_call_cancel))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lv_async_call_cancel &rArr; lv_timer_delete &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_timer_delete
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_timer_get_next
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>

<P><STRONG><a name="[147]"></a>lv_cache_acquire</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, lv_cache.o(i.lv_cache_acquire))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lv_cache_acquire &rArr; lv_cache_entry_acquire_data
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_acquire_data
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;try_cache
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
</UL>

<P><STRONG><a name="[14e]"></a>lv_cache_add</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, lv_cache.o(i.lv_cache_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lv_cache_add &rArr; cache_add_internal_no_lock &rArr; cache_evict_one_internal_no_lock &rArr; lv_cache_entry_delete &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_acquire_data
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cache_add_internal_no_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
</UL>

<P><STRONG><a name="[169]"></a>lv_cache_entry_acquire_data</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, lv_cache_entry.o(i.lv_cache_entry_acquire_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_cache_entry_acquire_data
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_get_data
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_inc_ref
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_add
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_acquire
</UL>

<P><STRONG><a name="[16d]"></a>lv_cache_entry_dec_ref</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, lv_cache_entry.o(i.lv_cache_entry_dec_ref))
<BR><BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_release_data
</UL>

<P><STRONG><a name="[e9]"></a>lv_cache_entry_delete</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, lv_cache_entry.o(i.lv_cache_entry_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = lv_cache_entry_delete &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_get_data
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_release
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cache_evict_one_internal_no_lock
</UL>

<P><STRONG><a name="[e8]"></a>lv_cache_entry_get_data</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lv_cache_entry.o(i.lv_cache_entry_get_data))
<BR><BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;try_cache
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_release
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_delete
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_acquire_data
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cache_evict_one_internal_no_lock
</UL>

<P><STRONG><a name="[16c]"></a>lv_cache_entry_get_ref</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_cache_entry.o(i.lv_cache_entry_get_ref))
<BR><BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_release
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_release_data
</UL>

<P><STRONG><a name="[16a]"></a>lv_cache_entry_inc_ref</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lv_cache_entry.o(i.lv_cache_entry_inc_ref))
<BR><BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_acquire_data
</UL>

<P><STRONG><a name="[16e]"></a>lv_cache_entry_is_invalid</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_cache_entry.o(i.lv_cache_entry_is_invalid))
<BR><BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_release
</UL>

<P><STRONG><a name="[16b]"></a>lv_cache_entry_release_data</STRONG> (Thumb, 32 bytes, Stack size 4 bytes, lv_cache_entry.o(i.lv_cache_entry_release_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_cache_entry_release_data
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_get_ref
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_dec_ref
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_release
</UL>

<P><STRONG><a name="[1af]"></a>lv_cache_is_enabled</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_cache.o(i.lv_cache_is_enabled))
<BR><BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_header_cache_is_enabled
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_cache_is_enabled
</UL>

<P><STRONG><a name="[148]"></a>lv_cache_release</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, lv_cache.o(i.lv_cache_release))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = lv_cache_release &rArr; lv_cache_entry_delete &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_get_data
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_get_ref
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_delete
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_release_data
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_is_invalid
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
</UL>

<P><STRONG><a name="[201]"></a>lv_clamp_height</STRONG> (Thumb, 124 bytes, Stack size 12 bytes, lv_obj_pos.o(i.lv_clamp_height))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lv_clamp_height
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
</UL>

<P><STRONG><a name="[200]"></a>lv_clamp_width</STRONG> (Thumb, 124 bytes, Stack size 12 bytes, lv_obj_pos.o(i.lv_clamp_width))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lv_clamp_width
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
</UL>

<P><STRONG><a name="[16f]"></a>lv_color_black</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, lv_color.o(i.lv_color_black))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lv_color_black &rArr; lv_color_make
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_make
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect_dsc_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image_dsc_init
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_palette_main
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_label_dsc_init
</UL>

<P><STRONG><a name="[171]"></a>lv_color_eq</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, lv_color.o(i.lv_color_eq))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lv_color_eq &rArr; lv_color_to_int
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_to_int
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_cb
</UL>

<P><STRONG><a name="[14f]"></a>lv_color_format_get_bpp</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, lv_color.o(i.lv_color_format_get_bpp))
<BR><BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;img_width_to_stride
</UL>

<P><STRONG><a name="[10e]"></a>lv_color_format_has_alpha</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, lv_color.o(i.lv_color_format_has_alpha))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[170]"></a>lv_color_make</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, lv_color.o(i.lv_color_make))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_color_make
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_rect_dsc
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_image_dsc
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;normal_apply_layer_recolor
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_black
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_white
</UL>

<P><STRONG><a name="[247]"></a>lv_color_mix</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, lv_color_op.o(i.lv_color_mix))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lv_color_mix
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;normal_apply_layer_recolor
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_cb
</UL>

<P><STRONG><a name="[173]"></a>lv_color_mix32</STRONG> (Thumb, 128 bytes, Stack size 8 bytes, lv_color_op.o(i.lv_color_mix32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_color_mix32
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_over32
</UL>

<P><STRONG><a name="[144]"></a>lv_color_over32</STRONG> (Thumb, 102 bytes, Stack size 20 bytes, lv_color_op.o(i.lv_color_over32))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = lv_color_over32 &rArr; lv_color_mix32
</UL>
<BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_mix32
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_apply_recolor
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_apply_layer_recolor
</UL>

<P><STRONG><a name="[143]"></a>lv_color_to_32</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, lv_color.o(i.lv_color_to_32))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lv_color_to_32
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_apply_recolor
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_recolor_recursive
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_apply_layer_recolor
</UL>

<P><STRONG><a name="[172]"></a>lv_color_to_int</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, lv_color.o(i.lv_color_to_int))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_color_to_int
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_eq
</UL>

<P><STRONG><a name="[174]"></a>lv_color_white</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, lv_color.o(i.lv_color_white))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lv_color_white &rArr; lv_color_make
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_make
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect_dsc_init
</UL>

<P><STRONG><a name="[15c]"></a>lv_cubic_bezier</STRONG> (Thumb, 268 bytes, Stack size 48 bytes, lv_math.o(i.lv_cubic_bezier))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = lv_cubic_bezier &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;do_cubic_bezier
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_path_cubic_bezier
</UL>

<P><STRONG><a name="[176]"></a>lv_display_enable_invalidation</STRONG> (Thumb, 36 bytes, Stack size 4 bytes, lv_display.o(i.lv_display_enable_invalidation))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_display_enable_invalidation
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_rotation
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scale_update
</UL>

<P><STRONG><a name="[177]"></a>lv_display_get_default</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, lv_display.o(i.lv_display_get_default))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_vertical_resolution
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_horizontal_resolution
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_create_obj
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_layer_bottom
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_inv_area
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_is_invalidation_enabled
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_screen_prev
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_screen_active
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_layer_top
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_layer_sys
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_layer_bottom
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_dpi
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_theme
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_enable_invalidation
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_theme_get_from_obj
</UL>

<P><STRONG><a name="[178]"></a>lv_display_get_dpi</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, lv_display.o(i.lv_display_get_dpi))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_display_get_dpi
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
</UL>

<P><STRONG><a name="[179]"></a>lv_display_get_horizontal_resolution</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, lv_display.o(i.lv_display_get_horizontal_resolution))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_display_get_horizontal_resolution
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_create_obj
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_inv_area
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[17a]"></a>lv_display_get_layer_bottom</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, lv_display.o(i.lv_display_get_layer_bottom))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_display_get_layer_bottom
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_layer_bottom
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
</UL>

<P><STRONG><a name="[17b]"></a>lv_display_get_layer_sys</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, lv_display.o(i.lv_display_get_layer_sys))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_display_get_layer_sys
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
</UL>

<P><STRONG><a name="[17c]"></a>lv_display_get_layer_top</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, lv_display.o(i.lv_display_get_layer_top))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_display_get_layer_top
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
</UL>

<P><STRONG><a name="[17d]"></a>lv_display_get_next</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_display.o(i.lv_display_get_next))
<BR><BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_next
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_dispatch
</UL>

<P><STRONG><a name="[17e]"></a>lv_display_get_screen_active</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, lv_display.o(i.lv_display_get_screen_active))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_display_get_screen_active
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
</UL>

<P><STRONG><a name="[17f]"></a>lv_display_get_screen_prev</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, lv_display.o(i.lv_display_get_screen_prev))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_display_get_screen_prev
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
</UL>

<P><STRONG><a name="[180]"></a>lv_display_get_theme</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, lv_display.o(i.lv_display_get_theme))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_display_get_theme
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_theme_get_from_obj
</UL>

<P><STRONG><a name="[181]"></a>lv_display_get_vertical_resolution</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, lv_display.o(i.lv_display_get_vertical_resolution))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_display_get_vertical_resolution
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_create_obj
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_inv_area
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[182]"></a>lv_display_is_invalidation_enabled</STRONG> (Thumb, 32 bytes, Stack size 4 bytes, lv_display.o(i.lv_display_is_invalidation_enabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_display_is_invalidation_enabled
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_inv_area
</UL>

<P><STRONG><a name="[183]"></a>lv_display_send_event</STRONG> (Thumb, 60 bytes, Stack size 48 bytes, lv_display.o(i.lv_display_send_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_send
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_mark_layout_as_dirty
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_inv_area
</UL>

<P><STRONG><a name="[184]"></a>lv_draw_add_task</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, lv_draw.o(i.lv_draw_add_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = lv_draw_add_task &rArr; lv_malloc_zeroed &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_zeroed
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_label
</UL>

<P><STRONG><a name="[103]"></a>lv_draw_buf_destroy</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, lv_draw_buf.o(i.lv_draw_buf_destroy))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = lv_draw_buf_destroy &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_buf_free
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_task
</UL>

<P><STRONG><a name="[186]"></a>lv_draw_buf_flush_cache</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, lv_draw_buf.o(i.lv_draw_buf_flush_cache))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = lv_draw_buf_flush_cache &rArr; draw_buf_get_full_area
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_buf_get_full_area
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_open
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_font_get_bitmap_fmt_txt
</UL>

<P><STRONG><a name="[187]"></a>lv_draw_buf_width_to_stride</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_draw_buf.o(i.lv_draw_buf_width_to_stride))
<BR><BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_buf_width_to_stride_ex
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_font_get_bitmap_fmt_txt
</UL>

<P><STRONG><a name="[188]"></a>lv_draw_buf_width_to_stride_ex</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lv_draw_buf.o(i.lv_draw_buf_width_to_stride_ex))
<BR><BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_buf_width_to_stride
</UL>

<P><STRONG><a name="[189]"></a>lv_draw_dispatch</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, lv_draw.o(i.lv_draw_dispatch))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lv_draw_dispatch &rArr; lv_draw_dispatch_layer &rArr; cleanup_task &rArr; lv_draw_buf_destroy &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_next
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_dispatch_request
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_wait_for_finish
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_dispatch_layer
</UL>
<BR>[Called By]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_finalize_task_creation
</UL>

<P><STRONG><a name="[18a]"></a>lv_draw_dispatch_layer</STRONG> (Thumb, 152 bytes, Stack size 24 bytes, lv_draw.o(i.lv_draw_dispatch_layer))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = lv_draw_dispatch_layer &rArr; cleanup_task &rArr; lv_draw_buf_destroy &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_dispatch_request
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_task
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_dispatch
</UL>

<P><STRONG><a name="[18c]"></a>lv_draw_dispatch_request</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_draw.o(i.lv_draw_dispatch_request))
<BR><BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_dispatch
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_dispatch_layer
</UL>

<P><STRONG><a name="[18d]"></a>lv_draw_fill_dsc_init</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, lv_draw_rect.o(i.lv_draw_fill_dsc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_draw_fill_dsc_init
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
</UL>

<P><STRONG><a name="[18e]"></a>lv_draw_finalize_task_creation</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, lv_draw.o(i.lv_draw_finalize_task_creation))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = lv_draw_finalize_task_creation &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_dispatch
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_label
</UL>

<P><STRONG><a name="[117]"></a>lv_draw_image</STRONG> (Thumb, 494 bytes, Stack size 296 bytes, lv_draw_image.o(i.lv_draw_image))
<BR><BR>[Stack]<UL><LI>Max Depth = 664<LI>Call Chain = lv_draw_image &rArr; lv_draw_finalize_task_creation &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memcpy
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_width
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_height
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_intersect
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_move
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_open
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_finalize_task_creation
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_add_task
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_get_info
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_buf_get_transformed_area
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[114]"></a>lv_draw_image_dsc_init</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, lv_draw_image.o(i.lv_draw_image_dsc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = lv_draw_image_dsc_init &rArr; lv_color_black &rArr; lv_color_make
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_black
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[11c]"></a>lv_draw_label</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, lv_draw_label.o(i.lv_draw_label))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = lv_draw_label &rArr; lv_draw_finalize_task_creation &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memcpy
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_finalize_task_creation
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_add_task
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_strndup
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[10b]"></a>lv_draw_label_dsc_init</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, lv_draw_label.o(i.lv_draw_label_dsc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = lv_draw_label_dsc_init &rArr; lv_palette_main &rArr; lv_color_black &rArr; lv_color_make
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_black
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_palette_main
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[121]"></a>lv_draw_rect</STRONG> (Thumb, 1176 bytes, Stack size 104 bytes, lv_draw_rect.o(i.lv_draw_rect))
<BR><BR>[Stack]<UL><LI>Max Depth = 472<LI>Call Chain = lv_draw_rect &rArr; lv_draw_finalize_task_creation &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_increase
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_src_get_type
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image_dsc_init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_move
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_fill_dsc_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_align
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_finalize_task_creation
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_add_task
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_get_info
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_size
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_label_dsc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_scrollbar
</UL>

<P><STRONG><a name="[195]"></a>lv_draw_rect_dsc_init</STRONG> (Thumb, 172 bytes, Stack size 16 bytes, lv_draw_rect.o(i.lv_draw_rect_dsc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = lv_draw_rect_dsc_init &rArr; lv_color_black &rArr; lv_color_make
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_black
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_white
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scrollbar_init_draw_dsc
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
</UL>

<P><STRONG><a name="[104]"></a>lv_draw_task_get_label_dsc</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lv_draw_label.o(i.lv_draw_task_get_label_dsc))
<BR><BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_task
</UL>

<P><STRONG><a name="[18b]"></a>lv_draw_wait_for_finish</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, lv_draw.o(i.lv_draw_wait_for_finish))
<BR><BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_dispatch
</UL>

<P><STRONG><a name="[108]"></a>lv_event_get_code</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_event.o(i.lv_event_get_code))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[109]"></a>lv_event_get_current_target</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_event.o(i.lv_event_get_current_target))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[196]"></a>lv_event_get_indev</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, lv_obj_event.o(i.lv_event_get_indev))
<BR><BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_param
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[197]"></a>lv_event_get_key</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, lv_obj_event.o(i.lv_event_get_key))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_event_get_key
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_param
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[10a]"></a>lv_event_get_layer</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, lv_obj_event.o(i.lv_event_get_layer))
<BR><BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[10d]"></a>lv_event_get_param</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_event.o(i.lv_event_get_param))
<BR><BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_set_ext_draw_size
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_key
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_indev
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[1e0]"></a>lv_event_mark_deleted</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, lv_event.o(i.lv_event_mark_deleted))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_destructor
</UL>

<P><STRONG><a name="[217]"></a>lv_event_pop</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_event.o(i.lv_event_pop))
<BR><BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
</UL>

<P><STRONG><a name="[216]"></a>lv_event_push</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_event.o(i.lv_event_push))
<BR><BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
</UL>

<P><STRONG><a name="[198]"></a>lv_event_remove_all</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, lv_event.o(i.lv_event_remove_all))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = lv_event_remove_all &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_mark_deleting
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_array_size
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_array_at
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_event_list
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_destructor
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>

<P><STRONG><a name="[127]"></a>lv_event_send</STRONG> (Thumb, 216 bytes, Stack size 56 bytes, lv_event.o(i.lv_event_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_is_marked_deleting
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_array_size
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_array_at
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_event_list_core
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_event_list
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send_core
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_send_event
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_send_event
</UL>

<P><STRONG><a name="[19b]"></a>lv_event_set_ext_draw_size</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, lv_obj_event.o(i.lv_event_set_ext_draw_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_event_set_ext_draw_size
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_param
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[81]"></a>lv_font_get_bitmap_fmt_txt</STRONG> (Thumb, 726 bytes, Stack size 32 bytes, lv_font_fmt_txt.o(i.lv_font_get_bitmap_fmt_txt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = lv_font_get_bitmap_fmt_txt &rArr; lv_draw_buf_flush_cache &rArr; draw_buf_get_full_area
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_buf_width_to_stride
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_buf_flush_cache
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_font_montserrat_14.o(.constdata)
</UL>
<P><STRONG><a name="[19c]"></a>lv_font_get_glyph_dsc</STRONG> (Thumb, 172 bytes, Stack size 32 bytes, lv_font.o(i.lv_font_get_glyph_dsc))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = lv_font_get_glyph_dsc
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_font_get_glyph_width
</UL>

<P><STRONG><a name="[80]"></a>lv_font_get_glyph_dsc_fmt_txt</STRONG> (Thumb, 186 bytes, Stack size 32 bytes, lv_font_fmt_txt.o(i.lv_font_get_glyph_dsc_fmt_txt))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = lv_font_get_glyph_dsc_fmt_txt &rArr; get_glyph_dsc_id &rArr; lv_utils_bsearch
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_kern_value
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_glyph_dsc_id
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_font_montserrat_14.o(.constdata)
</UL>
<P><STRONG><a name="[19d]"></a>lv_font_get_glyph_width</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, lv_font.o(i.lv_font_get_glyph_width))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = lv_font_get_glyph_width &rArr; lv_font_get_glyph_dsc
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_font_get_glyph_dsc
</UL>
<BR>[Called By]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_width_with_flags
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_next_line
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_next_word
</UL>

<P><STRONG><a name="[23f]"></a>lv_font_get_line_height</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_font.o(i.lv_font_get_line_height))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_size
</UL>

<P><STRONG><a name="[ff]"></a>lv_free</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lv_mem.o(i.lv_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free_core
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_create_obj
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_obj_state
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_destructor
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_remove_obj
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_reset
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_remove_prop
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_delete
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_completed_cb
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_async_call_cancel
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_timer_delete
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_buf_destroy
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_task
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_close
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_delete
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_concurrent_anims
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_anim
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_async_timer_cb
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_array_deinit
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_event_list_core
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_destructor
</UL>

<P><STRONG><a name="[19e]"></a>lv_free_core</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, lv_mem_core_builtin.o(i.lv_free_core))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_free
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_block_size
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
</UL>

<P><STRONG><a name="[14c]"></a>lv_fs_close</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, lv_fs.o(i.lv_fs_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = lv_fs_close &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
</UL>

<P><STRONG><a name="[1a1]"></a>lv_fs_get_drv</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, lv_fs.o(i.lv_fs_get_drv))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lv_fs_get_drv
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_next
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_open
</UL>

<P><STRONG><a name="[149]"></a>lv_fs_open</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, lv_fs.o(i.lv_fs_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lv_fs_open &rArr; lv_malloc_zeroed &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_zeroed
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_get_drv
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_resolve_path
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
</UL>

<P><STRONG><a name="[14a]"></a>lv_fs_seek</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, lv_fs.o(i.lv_fs_seek))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = lv_fs_seek &rArr; lv_fs_seek_cached
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_seek_cached
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
</UL>

<P><STRONG><a name="[1a4]"></a>lv_group_add_obj</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, lv_group.o(i.lv_group_add_obj))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_group_add_obj &rArr; lv_group_remove_obj &rArr; lv_group_refocus &rArr; lv_group_focus_prev &rArr; focus_next_core &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_allocate_spec_attr
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_ins_tail
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_remove_obj
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_refocus
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
</UL>

<P><STRONG><a name="[1a9]"></a>lv_group_focus_next</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, lv_group.o(i.lv_group_focus_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = lv_group_focus_next &rArr; focus_next_core &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;focus_next_core
</UL>
<BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_refocus
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
</UL>

<P><STRONG><a name="[1aa]"></a>lv_group_focus_prev</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, lv_group.o(i.lv_group_focus_prev))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = lv_group_focus_prev &rArr; focus_next_core &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;focus_next_core
</UL>
<BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_refocus
</UL>

<P><STRONG><a name="[1d8]"></a>lv_group_get_default</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, lv_group.o(i.lv_group_get_default))
<BR><BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
</UL>

<P><STRONG><a name="[1ec]"></a>lv_group_get_editing</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lv_group.o(i.lv_group_get_editing))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[1c9]"></a>lv_group_get_focused</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_group.o(i.lv_group_get_focused))
<BR><BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
</UL>

<P><STRONG><a name="[1a5]"></a>lv_group_remove_obj</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, lv_group.o(i.lv_group_remove_obj))
<BR><BR>[Stack]<UL><LI>Max Depth = 528<LI>Call Chain = lv_group_remove_obj &rArr; lv_group_refocus &rArr; lv_group_focus_prev &rArr; focus_next_core &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_group
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_tail
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_next
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_refocus
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_indev
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_destructor
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_add_obj
</UL>

<P><STRONG><a name="[112]"></a>lv_image_buf_get_transformed_area</STRONG> (Thumb, 408 bytes, Stack size 72 bytes, lv_draw_image.o(i.lv_image_buf_get_transformed_area))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lv_image_buf_get_transformed_area &rArr; lv_point_transform &rArr; lv_point_array_transform
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_point_transform
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_rotation
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scale_update
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[1ae]"></a>lv_image_cache_is_enabled</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_image_cache.o(i.lv_image_cache_is_enabled))
<BR><BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_is_enabled
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_open
</UL>

<P><STRONG><a name="[1b2]"></a>lv_image_create</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lv_image.o(i.lv_image_create))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = lv_image_create &rArr; lv_obj_class_init_obj &rArr; lv_theme_apply &rArr; lv_obj_remove_style_all &rArr; lv_obj_remove_style &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_create_obj
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
</UL>

<P><STRONG><a name="[18f]"></a>lv_image_decoder_get_info</STRONG> (Thumb, 52 bytes, Stack size 88 bytes, lv_image_decoder.o(i.lv_image_decoder_get_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = lv_image_decoder_get_info &rArr; image_decoder_get_info &rArr; lv_fs_open &rArr; lv_malloc_zeroed &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_src_get_type
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image
</UL>

<P><STRONG><a name="[190]"></a>lv_image_decoder_open</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, lv_image_decoder.o(i.lv_image_decoder_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = lv_image_decoder_open &rArr; image_decoder_get_info &rArr; lv_fs_open &rArr; lv_malloc_zeroed &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_src_get_type
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_buf_flush_cache
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;try_cache
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_cache_is_enabled
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image
</UL>

<P><STRONG><a name="[10f]"></a>lv_image_get_pivot</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, lv_image.o(i.lv_image_get_pivot))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lv_image_get_pivot
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_pct_to_px
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_rotation
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scale_update
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[118]"></a>lv_image_get_scale</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_image.o(i.lv_image_get_scale))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[146]"></a>lv_image_header_cache_is_enabled</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_image_header_cache.o(i.lv_image_header_cache_is_enabled))
<BR><BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_is_enabled
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
</UL>

<P><STRONG><a name="[1bb]"></a>lv_image_set_pivot</STRONG> (Thumb, 278 bytes, Stack size 72 bytes, lv_image.o(i.lv_image_set_pivot))
<BR><BR>[Stack]<UL><LI>Max Depth = 760<LI>Call Chain = lv_image_set_pivot &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layout
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_enable_invalidation
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_point_set
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_buf_get_transformed_area
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_get_pivot
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_align
</UL>

<P><STRONG><a name="[1bf]"></a>lv_image_set_rotation</STRONG> (Thumb, 282 bytes, Stack size 64 bytes, lv_image.o(i.lv_image_set_rotation))
<BR><BR>[Stack]<UL><LI>Max Depth = 752<LI>Call Chain = lv_image_set_rotation &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layout
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_enable_invalidation
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_buf_get_transformed_area
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_get_pivot
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_align
</UL>

<P><STRONG><a name="[1b7]"></a>lv_image_set_src</STRONG> (Thumb, 358 bytes, Stack size 64 bytes, lv_image.o(i.lv_image_set_src))
<BR><BR>[Stack]<UL><LI>Max Depth = 840<LI>Call Chain = lv_image_set_src &rArr; update_align &rArr; lv_image_set_pivot &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_self_size
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_src_get_type
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_get_info
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_size
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_strdup
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_align
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
</UL>

<P><STRONG><a name="[194]"></a>lv_image_src_get_type</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, lv_draw_image.o(i.lv_image_src_get_type))
<BR><BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_rect_dsc
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_open
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_get_info
</UL>

<P><STRONG><a name="[126]"></a>lv_indev_active</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, lv_indev.o(i.lv_indev_active))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send_core
</UL>

<P><STRONG><a name="[24a]"></a>lv_indev_get_active_obj</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, lv_indev.o(i.lv_indev_get_active_obj))
<BR><BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>

<P><STRONG><a name="[133]"></a>lv_indev_get_group</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lv_indev.o(i.lv_indev_get_group))
<BR><BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_indev
</UL>

<P><STRONG><a name="[131]"></a>lv_indev_get_next</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, lv_indev.o(i.lv_indev_get_next))
<BR><BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_next
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_indev
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_reset
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>

<P><STRONG><a name="[1f6]"></a>lv_indev_get_scroll_dir</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, lv_indev.o(i.lv_indev_get_scroll_dir))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
</UL>

<P><STRONG><a name="[1e5]"></a>lv_indev_get_scroll_obj</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, lv_indev.o(i.lv_indev_get_scroll_obj))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[24c]"></a>lv_indev_get_state</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lv_indev.o(i.lv_indev_get_state))
<BR><BR>[Called By]<UL><LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_indev_reset
</UL>

<P><STRONG><a name="[132]"></a>lv_indev_get_type</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lv_indev.o(i.lv_indev_get_type))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_indev
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>

<P><STRONG><a name="[1c1]"></a>lv_indev_reset</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, lv_indev.o(i.lv_indev_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = lv_indev_reset &rArr; indev_reset_core &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_next
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;indev_reset_core
</UL>
<BR>[Called By]<UL><LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_indev_reset
</UL>

<P><STRONG><a name="[151]"></a>lv_indev_send_event</STRONG> (Thumb, 60 bytes, Stack size 48 bytes, lv_indev.o(i.lv_indev_send_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = lv_indev_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_send
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;indev_reset_core
</UL>

<P><STRONG><a name="[24d]"></a>lv_indev_wait_release</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lv_indev.o(i.lv_indev_wait_release))
<BR><BR>[Called By]<UL><LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_indev_reset
</UL>

<P><STRONG><a name="[1c2]"></a>lv_inv_area</STRONG> (Thumb, 276 bytes, Stack size 48 bytes, lv_refr.o(i.lv_inv_area))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_vertical_resolution
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_horizontal_resolution
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_in
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_send_event
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_is_invalidation_enabled
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_intersect
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
</UL>

<P><STRONG><a name="[1c3]"></a>lv_layer_bottom</STRONG> (Thumb, 14 bytes, Stack size 4 bytes, lv_display.o(i.lv_layer_bottom))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_layer_bottom &rArr; lv_display_get_layer_bottom
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_layer_bottom
</UL>
<BR>[Called By]<UL><LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_bottom_layer
</UL>

<P><STRONG><a name="[155]"></a>lv_layout_apply</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, lv_layout.o(i.lv_layout_apply))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = lv_layout_apply &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;layout_update_core
</UL>

<P><STRONG><a name="[6d]"></a>lv_ll_get_head</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lv_ll.o(i.lv_ll_get_head))
<BR><BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_next
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_delete
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_next
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_remove_obj
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_add_obj
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_completed_cb
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_concurrent_anims
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anim_mark_list_change
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_timer_get_next
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_get_drv
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_group.o(i.lv_group_focus_next)
</UL>
<P><STRONG><a name="[6c]"></a>lv_ll_get_next</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_ll.o(i.lv_ll_get_next))
<BR><BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_next
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_delete
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_next
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_remove_obj
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_completed_cb
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_concurrent_anims
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_timer_get_next
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_get_drv
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_group.o(i.lv_group_focus_next)
</UL>
<P><STRONG><a name="[6e]"></a>lv_ll_get_prev</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_ll.o(i.lv_ll_get_prev))
<BR><BR>[Called By]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_delete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_group.o(i.lv_group_focus_prev)
</UL>
<P><STRONG><a name="[6f]"></a>lv_ll_get_tail</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lv_ll.o(i.lv_ll_get_tail))
<BR><BR>[Called By]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_remove_obj
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_delete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_group.o(i.lv_group_focus_prev)
</UL>
<P><STRONG><a name="[15f]"></a>lv_ll_ins_head</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, lv_ll.o(i.lv_ll_ins_head))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lv_ll_ins_head &rArr; lv_malloc &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;node_set_prev
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;node_set_next
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_start
</UL>

<P><STRONG><a name="[1a7]"></a>lv_ll_ins_tail</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, lv_ll.o(i.lv_ll_ins_tail))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lv_ll_ins_tail &rArr; lv_malloc &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;node_set_prev
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;node_set_next
</UL>
<BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_add_obj
</UL>

<P><STRONG><a name="[1ac]"></a>lv_ll_remove</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, lv_ll.o(i.lv_ll_remove))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = lv_ll_remove &rArr; node_set_prev
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_tail
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_prev
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_next
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;node_set_prev
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;node_set_next
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_remove_obj
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_delete
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_completed_cb
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_timer_delete
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_concurrent_anims
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_anim
</UL>

<P><STRONG><a name="[13f]"></a>lv_malloc</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, lv_mem.o(i.lv_malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = lv_malloc &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_core
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_ins_tail
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_ins_head
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_remove_prop
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_trans_style
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_strndup
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_strdup
</UL>

<P><STRONG><a name="[1c6]"></a>lv_malloc_core</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, lv_mem_core_builtin.o(i.lv_malloc_core))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_malloc
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_block_size
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_zeroed
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc
</UL>

<P><STRONG><a name="[13a]"></a>lv_malloc_zeroed</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, lv_mem.o(i.lv_malloc_zeroed))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = lv_malloc_zeroed &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_core
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_create_obj
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_obj_state
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_allocate_spec_attr
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_local_style
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_add_task
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_open
</UL>

<P><STRONG><a name="[15b]"></a>lv_map</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, lv_math.o(i.lv_map))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_path_linear
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_path_cubic_bezier
</UL>

<P><STRONG><a name="[160]"></a>lv_memcpy</STRONG> (Thumb, 596 bytes, Stack size 4 bytes, lv_string_builtin.o(i.lv_memcpy))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_rect_dsc
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_start
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_strndup
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_label
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_strdup
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
</UL>

<P><STRONG><a name="[139]"></a>lv_memset</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, lv_string_builtin.o(i.lv_memset))
<BR><BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_zeroed
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect_dsc_init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_reset
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_init
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_send_event
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_trans_style
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_local_style
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image_dsc_init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_open
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_fill_dsc_init
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_get_info
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_font_get_glyph_dsc
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_label_dsc_init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_send_event
</UL>

<P><STRONG><a name="[1b1]"></a>lv_obj_add_flag</STRONG> (Thumb, 164 bytes, Stack size 56 bytes, lv_obj.o(i.lv_obj_add_flag))
<BR><BR>[Stack]<UL><LI>Max Depth = 560<LI>Call Chain = lv_obj_add_flag &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_mark_layout_as_dirty
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_is_layout_positioned
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_state
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_group
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_get_focused
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_focus_next
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_constructor
</UL>

<P><STRONG><a name="[1cb]"></a>lv_obj_add_state</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_obj.o(i.lv_obj_add_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 696<LI>Call Chain = lv_obj_add_state &rArr; update_obj_state &rArr; lv_obj_style_create_transition &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_obj_state
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[1a6]"></a>lv_obj_allocate_spec_attr</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, lv_obj.o(i.lv_obj_allocate_spec_attr))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lv_obj_allocate_spec_attr &rArr; lv_malloc_zeroed &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_zeroed
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layer_type
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_create_obj
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_add_obj
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_scrollbar_mode
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_raw
</UL>

<P><STRONG><a name="[1cd]"></a>lv_obj_area_is_visible</STRONG> (Thumb, 284 bytes, Stack size 56 bytes, lv_obj_pos.o(i.lv_obj_area_is_visible))
<BR><BR>[Stack]<UL><LI>Max Depth = 292<LI>Call Chain = lv_obj_area_is_visible &rArr; lv_obj_get_transformed_area &rArr; lv_obj_transform_point_array &rArr;  lv_obj_transform_point_array (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_increase
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_ext_draw_size
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_screen
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_screen_prev
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_screen_active
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_layer_top
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_layer_sys
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_layer_bottom
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_intersect
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_transformed_area
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_transformed
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
</UL>

<P><STRONG><a name="[1d2]"></a>lv_obj_calculate_ext_draw_size</STRONG> (Thumb, 250 bytes, Stack size 32 bytes, lv_obj_draw.o(i.lv_obj_calculate_ext_draw_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = lv_obj_calculate_ext_draw_size &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[1b3]"></a>lv_obj_class_create_obj</STRONG> (Thumb, 180 bytes, Stack size 16 bytes, lv_obj_class.o(i.lv_obj_class_create_obj))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = lv_obj_class_create_obj &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_vertical_resolution
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_horizontal_resolution
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_instance_size
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_zeroed
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_allocate_spec_attr
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_create
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_create
</UL>

<P><STRONG><a name="[1b4]"></a>lv_obj_class_init_obj</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, lv_obj_class.o(i.lv_obj_class_init_obj))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = lv_obj_class_init_obj &rArr; lv_theme_apply &rArr; lv_obj_remove_style_all &rArr; lv_obj_remove_style &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_self_size
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_is_group_def
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_construct
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_mark_layout_as_dirty
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_enable_style_refresh
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_get_default
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_add_obj
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_theme_apply
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_create
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_create
</UL>

<P><STRONG><a name="[1dc]"></a>lv_obj_create</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, lv_obj.o(i.lv_obj_create))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = lv_obj_create &rArr; lv_obj_class_init_obj &rArr; lv_theme_apply &rArr; lv_obj_remove_style_all &rArr; lv_obj_remove_style &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_create_obj
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
</UL>

<P><STRONG><a name="[1dd]"></a>lv_obj_delete</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, lv_obj_tree.o(i.lv_obj_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = lv_obj_delete &rArr; lv_obj_scrollbar_invalidate &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scrollbar_invalidate
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_delete_async_cb
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scr_anim_completed
</UL>

<P><STRONG><a name="[24b]"></a>lv_obj_destruct</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, lv_obj_class.o(i.lv_obj_destruct))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_obj_destruct
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>

<P><STRONG><a name="[1d4]"></a>lv_obj_enable_style_refresh</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_obj_style.o(i.lv_obj_enable_style_refresh))
<BR><BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_destructor
</UL>

<P><STRONG><a name="[128]"></a>lv_obj_event_base</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, lv_obj_event.o(i.lv_obj_event_base))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_obj_event_base
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send_core
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
</UL>

<P><STRONG><a name="[248]"></a>lv_obj_get_child</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, lv_obj_tree.o(i.lv_obj_get_child))
<BR><BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>

<P><STRONG><a name="[ef]"></a>lv_obj_get_child_count</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lv_obj_tree.o(i.lv_obj_get_child_count))
<BR><BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_right
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_children_by
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;layout_update_core
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_width
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_height
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;refresh_children_style
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_bottom
</UL>

<P><STRONG><a name="[1b9]"></a>lv_obj_get_click_area</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, lv_obj_pos.o(i.lv_obj_get_click_area))
<BR><BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_increase
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
</UL>

<P><STRONG><a name="[1ee]"></a>lv_obj_get_content_coords</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_get_content_coords))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = lv_obj_get_content_coords &rArr; lv_obj_get_style_space_top &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_coords
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_top
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_right
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_left
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_bottom
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
</UL>

<P><STRONG><a name="[1f0]"></a>lv_obj_get_content_height</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_get_content_height))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = lv_obj_get_content_height &rArr; lv_obj_get_style_space_top &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_top
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_bottom
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_pos
</UL>

<P><STRONG><a name="[1f1]"></a>lv_obj_get_content_width</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_get_content_width))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = lv_obj_get_content_width &rArr; lv_obj_get_style_space_right &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_right
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_left
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_pos
</UL>

<P><STRONG><a name="[1ef]"></a>lv_obj_get_coords</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, lv_obj_pos.o(i.lv_obj_get_coords))
<BR><BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_coords
</UL>

<P><STRONG><a name="[1be]"></a>lv_obj_get_display</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, lv_obj_tree.o(i.lv_obj_get_display))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = lv_obj_get_display &rArr; lv_obj_get_screen
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_next
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_screen
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_mark_layout_as_dirty
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_rotation
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_delete
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scr_load_internal
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scr_load_anim_start
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scr_anim_completed
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_theme_get_from_obj
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scale_update
</UL>

<P><STRONG><a name="[1cf]"></a>lv_obj_get_ext_draw_size</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lv_obj_draw.o(i.lv_obj_get_ext_draw_size))
<BR><BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
</UL>

<P><STRONG><a name="[1ab]"></a>lv_obj_get_group</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lv_obj.o(i.lv_obj_get_group))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_destructor
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_remove_obj
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>

<P><STRONG><a name="[110]"></a>lv_obj_get_height</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lv_obj_pos.o(i.lv_obj_get_height))
<BR><BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_height
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_rotation
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_pos
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_height
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_bottom
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_align
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scale_update
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_constructor
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[1f2]"></a>lv_obj_get_index</STRONG> (Thumb, 54 bytes, Stack size 4 bytes, lv_obj_tree.o(i.lv_obj_get_index))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_obj_get_index
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>

<P><STRONG><a name="[230]"></a>lv_obj_get_layer_type</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lv_obj_draw.o(i.lv_obj_get_layer_type))
<BR><BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_transform_point_array
</UL>

<P><STRONG><a name="[1f3]"></a>lv_obj_get_local_style_prop</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, lv_obj_style.o(i.lv_obj_get_local_style_prop))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = lv_obj_get_local_style_prop &rArr; lv_style_get_prop
</UL>
<BR>[Calls]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_get_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_y
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_x
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_width
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_height
</UL>

<P><STRONG><a name="[129]"></a>lv_obj_get_parent</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lv_obj_tree.o(i.lv_obj_get_parent))
<BR><BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_view_recursive
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_is_layout_positioned
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_opa_recursive
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_flag
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send_core
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_recolor_recursive
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;focus_next_core
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_screen
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_transform_point_array
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_pos
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_index
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_delete
</UL>

<P><STRONG><a name="[1ce]"></a>lv_obj_get_screen</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, lv_obj_tree.o(i.lv_obj_get_screen))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = lv_obj_get_screen
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_mark_layout_as_dirty
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layout
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
</UL>

<P><STRONG><a name="[1f5]"></a>lv_obj_get_scroll_bottom</STRONG> (Thumb, 240 bytes, Stack size 40 bytes, lv_obj_scroll.o(i.lv_obj_get_scroll_bottom))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = lv_obj_get_scroll_bottom &rArr; lv_obj_get_self_height &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_y
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_child_count
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag_any
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_self_height
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_readjust_scroll
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_bounded
</UL>

<P><STRONG><a name="[1e8]"></a>lv_obj_get_scroll_dir</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lv_obj_scroll.o(i.lv_obj_get_scroll_dir))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
</UL>

<P><STRONG><a name="[1da]"></a>lv_obj_get_scroll_left</STRONG> (Thumb, 280 bytes, Stack size 40 bytes, lv_obj_scroll.o(i.lv_obj_get_scroll_left))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = lv_obj_get_scroll_left &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_x
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_child_count
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag_any
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_self_width
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_constructor
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_readjust_scroll
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_bounded
</UL>

<P><STRONG><a name="[1e7]"></a>lv_obj_get_scroll_right</STRONG> (Thumb, 276 bytes, Stack size 40 bytes, lv_obj_scroll.o(i.lv_obj_get_scroll_right))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_x
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_child_count
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag_any
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_self_width
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_readjust_scroll
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_bounded
</UL>

<P><STRONG><a name="[1ff]"></a>lv_obj_get_scroll_snap_x</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_obj_scroll.o(i.lv_obj_get_scroll_snap_x))
<BR><BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_readjust_scroll
</UL>

<P><STRONG><a name="[1fd]"></a>lv_obj_get_scroll_snap_y</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_obj_scroll.o(i.lv_obj_get_scroll_snap_y))
<BR><BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_readjust_scroll
</UL>

<P><STRONG><a name="[1db]"></a>lv_obj_get_scroll_top</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lv_obj_scroll.o(i.lv_obj_get_scroll_top))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_constructor
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_readjust_scroll
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_bounded
</UL>

<P><STRONG><a name="[f5]"></a>lv_obj_get_scroll_x</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lv_obj_scroll.o(i.lv_obj_get_scroll_x))
<BR><BR>[Called By]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_x
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_right
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_x_anim
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_width
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_bounded
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[eb]"></a>lv_obj_get_scroll_y</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lv_obj_scroll.o(i.lv_obj_get_scroll_y))
<BR><BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_y
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_y_anim
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_height
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_bounded
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_bottom
</UL>

<P><STRONG><a name="[11e]"></a>lv_obj_get_scrollbar_area</STRONG> (Thumb, 1898 bytes, Stack size 104 bytes, lv_obj_scroll.o(i.lv_obj_get_scrollbar_area))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_mode
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_top
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_right
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_dir
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_scroll_obj
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_next
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_scroll_dir
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_dpi
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_set
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_bottom
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_flag
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_scrollbar
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scrollbar_invalidate
</UL>

<P><STRONG><a name="[1ed]"></a>lv_obj_get_scrollbar_mode</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_obj_scroll.o(i.lv_obj_get_scrollbar_mode))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[ee]"></a>lv_obj_get_self_height</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_get_self_height))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = lv_obj_get_self_height &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_height
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_bottom
</UL>

<P><STRONG><a name="[f8]"></a>lv_obj_get_self_width</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_get_self_width))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_right
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_width
</UL>

<P><STRONG><a name="[12b]"></a>lv_obj_get_state</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_obj.o(i.lv_obj_get_state))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;focus_next_core
</UL>

<P><STRONG><a name="[136]"></a>lv_obj_get_style_opa_recursive</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, lv_obj_style.o(i.lv_obj_get_style_opa_recursive))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = lv_obj_get_style_opa_recursive &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scrollbar_init_draw_dsc
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_layer_opa
</UL>

<P><STRONG><a name="[f2]"></a>lv_obj_get_style_prop</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, lv_obj_style.o(i.lv_obj_get_style_prop))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_prop_get_default
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_selector_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_self_size
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_is_layout_positioned
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_rect_dsc
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_opa_recursive
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_right
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_calculate_ext_draw_size
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scrollbar_init_draw_dsc
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_constructor
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_apply_recolor
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_recolor_recursive
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_label_dsc
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_image_dsc
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_layer_opa
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_readjust_scroll
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_layout_apply
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_pos
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;transform_point_array
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_top
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_right
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_left
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_bottom
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_width
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_height
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_start_cb
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_layer_type
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_bounded
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_bottom
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[142]"></a>lv_obj_get_style_recolor_recursive</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, lv_obj_style.o(i.lv_obj_get_style_recolor_recursive))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = lv_obj_get_style_recolor_recursive &rArr; lv_obj_style_apply_recolor &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_apply_recolor
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_to_32
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;normal_apply_layer_recolor
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_apply_layer_recolor
</UL>

<P><STRONG><a name="[1d1]"></a>lv_obj_get_transformed_area</STRONG> (Thumb, 304 bytes, Stack size 40 bytes, lv_obj_pos.o(i.lv_obj_get_transformed_area))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = lv_obj_get_transformed_area &rArr; lv_obj_transform_point_array &rArr;  lv_obj_transform_point_array (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_transform_point_array
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
</UL>

<P><STRONG><a name="[111]"></a>lv_obj_get_width</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lv_obj_pos.o(i.lv_obj_get_width))
<BR><BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_width
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_right
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_rotation
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_pos
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_width
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_align
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scale_update
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_constructor
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[124]"></a>lv_obj_has_flag</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, lv_obj.o(i.lv_obj_has_flag))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_is_bubbled
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;focus_next_core
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_children_by
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_finalize_task_creation
</UL>

<P><STRONG><a name="[f0]"></a>lv_obj_has_flag_any</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lv_obj.o(i.lv_obj_has_flag_any))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_is_layout_positioned
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_right
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_flag
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_width
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_height
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_bottom
</UL>

<P><STRONG><a name="[1c8]"></a>lv_obj_has_state</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lv_obj.o(i.lv_obj_has_state))
<BR><BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
</UL>

<P><STRONG><a name="[115]"></a>lv_obj_init_draw_image_dsc</STRONG> (Thumb, 244 bytes, Stack size 32 bytes, lv_obj_draw.o(i.lv_obj_init_draw_image_dsc))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = lv_obj_init_draw_image_dsc &rArr; image_apply_layer_recolor &rArr; lv_obj_get_style_recolor_recursive &rArr; lv_obj_style_apply_recolor &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_apply_color_filter
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_make
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_width
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_height
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_apply_layer_recolor
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_layer_opa
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[10c]"></a>lv_obj_init_draw_label_dsc</STRONG> (Thumb, 196 bytes, Stack size 24 bytes, lv_obj_draw.o(i.lv_obj_init_draw_label_dsc))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = lv_obj_init_draw_label_dsc &rArr; normal_apply_layer_recolor &rArr; lv_obj_get_style_recolor_recursive &rArr; lv_obj_style_apply_recolor &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_apply_color_filter
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;normal_apply_layer_recolor
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_layer_opa
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[1e3]"></a>lv_obj_init_draw_rect_dsc</STRONG> (Thumb, 1072 bytes, Stack size 40 bytes, lv_obj_draw.o(i.lv_obj_init_draw_rect_dsc))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = lv_obj_init_draw_rect_dsc &rArr; normal_apply_layer_recolor &rArr; lv_obj_get_style_recolor_recursive &rArr; lv_obj_style_apply_recolor &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_apply_color_filter
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memcpy
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_src_get_type
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_make
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;normal_apply_layer_recolor
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_apply_layer_recolor
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_layer_opa
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
</UL>

<P><STRONG><a name="[12e]"></a>lv_obj_invalidate</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, lv_obj_pos.o(i.lv_obj_invalidate))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_ext_draw_size
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_flag
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_obj_state
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;focus_next_core
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_scrollbar_mode
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;refresh_children_style
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_raw
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_delete
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scr_load_internal
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scr_anim_completed
</UL>

<P><STRONG><a name="[1bd]"></a>lv_obj_invalidate_area</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, lv_obj_pos.o(i.lv_obj_invalidate_area))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_increase
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_inv_area
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_is_invalidation_enabled
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_flag
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_rotation
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scrollbar_invalidate
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scale_update
</UL>

<P><STRONG><a name="[1e6]"></a>lv_obj_is_editable</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, lv_obj_class.o(i.lv_obj_is_editable))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[1d9]"></a>lv_obj_is_group_def</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, lv_obj_class.o(i.lv_obj_is_group_def))
<BR><BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
</UL>

<P><STRONG><a name="[f1]"></a>lv_obj_is_layout_positioned</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_is_layout_positioned))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = lv_obj_is_layout_positioned &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag_any
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_flag
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_pos
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_width
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_height
</UL>

<P><STRONG><a name="[1ca]"></a>lv_obj_mark_layout_as_dirty</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, lv_obj_pos.o(i.lv_obj_mark_layout_as_dirty))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = lv_obj_mark_layout_as_dirty &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_screen
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_send_event
</UL>
<BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_self_size
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_flag
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
</UL>

<P><STRONG><a name="[1fb]"></a>lv_obj_move_children_by</STRONG> (Thumb, 102 bytes, Stack size 32 bytes, lv_obj_pos.o(i.lv_obj_move_children_by))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + In Cycle
<LI>Call Chain = lv_obj_move_children_by &rArr;  lv_obj_move_children_by (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_child_count
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_children_by
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_children_by
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_raw
</UL>

<P><STRONG><a name="[1fc]"></a>lv_obj_move_to</STRONG> (Thumb, 244 bytes, Stack size 64 bytes, lv_obj_pos.o(i.lv_obj_move_to))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = lv_obj_move_to &rArr; lv_obj_scrollbar_invalidate &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_y
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_x
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_in
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scrollbar_invalidate
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_children_by
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_coords
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_coords
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_top
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_left
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_pos
</UL>

<P><STRONG><a name="[156]"></a>lv_obj_readjust_scroll</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, lv_obj_scroll.o(i.lv_obj_readjust_scroll))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = lv_obj_readjust_scroll &rArr; lv_obj_scroll_by &rArr; lv_obj_scroll_by_raw &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_top
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_right
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_snap_y
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_snap_x
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_bottom
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;layout_update_core
</UL>

<P><STRONG><a name="[154]"></a>lv_obj_refr_pos</STRONG> (Thumb, 614 bytes, Stack size 48 bytes, lv_obj_pos.o(i.lv_obj_refr_pos))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = lv_obj_refr_pos &rArr; lv_obj_move_to &rArr; lv_obj_scrollbar_invalidate &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_is_layout_positioned
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_width
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_height
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;layout_update_core
</UL>

<P><STRONG><a name="[153]"></a>lv_obj_refr_size</STRONG> (Thumb, 756 bytes, Stack size 80 bytes, lv_obj_pos.o(i.lv_obj_refr_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = lv_obj_refr_size &rArr; lv_obj_scrollbar_invalidate &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_in
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scrollbar_invalidate
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_coords
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_width
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_height
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_coords
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_clamp_width
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_clamp_height
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_top
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_right
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_left
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_bottom
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_width
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_height
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;layout_update_core
</UL>

<P><STRONG><a name="[1b6]"></a>lv_obj_refresh_ext_draw_size</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, lv_obj_draw.o(i.lv_obj_refresh_ext_draw_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = lv_obj_refresh_ext_draw_size &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_allocate_spec_attr
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_ext_draw_size
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_obj_state
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_rotation
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scale_update
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
</UL>

<P><STRONG><a name="[1c0]"></a>lv_obj_refresh_self_size</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_refresh_self_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = lv_obj_refresh_self_size &rArr; lv_obj_mark_layout_as_dirty &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_mark_layout_as_dirty
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
</UL>

<P><STRONG><a name="[1d7]"></a>lv_obj_refresh_style</STRONG> (Thumb, 288 bytes, Stack size 40 bytes, lv_obj_style.o(i.lv_obj_refresh_style))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layer_type
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_mark_layout_as_dirty
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_prop_lookup_flags
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;refresh_children_style
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_obj_state
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_local_style_prop
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_start_cb
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_cb
</UL>

<P><STRONG><a name="[1b0]"></a>lv_obj_remove_flag</STRONG> (Thumb, 136 bytes, Stack size 48 bytes, lv_obj.o(i.lv_obj_remove_flag))
<BR><BR>[Stack]<UL><LI>Max Depth = 552<LI>Call Chain = lv_obj_remove_flag &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_mark_layout_as_dirty
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_is_layout_positioned
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag_any
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_constructor
</UL>

<P><STRONG><a name="[205]"></a>lv_obj_remove_local_style_prop</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, lv_obj_style.o(i.lv_obj_remove_local_style_prop))
<BR><BR>[Stack]<UL><LI>Max Depth = 528<LI>Call Chain = lv_obj_remove_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_remove_prop
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;full_cache_refresh
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scr_anim_completed
</UL>

<P><STRONG><a name="[1e4]"></a>lv_obj_remove_state</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, lv_obj.o(i.lv_obj_remove_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 696<LI>Call Chain = lv_obj_remove_state &rArr; update_obj_state &rArr; lv_obj_style_create_transition &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_obj_state
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[208]"></a>lv_obj_remove_style</STRONG> (Thumb, 304 bytes, Stack size 40 bytes, lv_obj_style.o(i.lv_obj_remove_style))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_remove_style &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_reset
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_delete
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;style_has_flag
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;full_cache_refresh
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style_all
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_completed_cb
</UL>

<P><STRONG><a name="[1e1]"></a>lv_obj_remove_style_all</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_obj_style.o(i.lv_obj_remove_style_all))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_remove_style_all &rArr; lv_obj_remove_style &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_destructor
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_theme_apply
</UL>

<P><STRONG><a name="[1fe]"></a>lv_obj_scroll_by</STRONG> (Thumb, 276 bytes, Stack size 120 bytes, lv_obj_scroll.o(i.lv_obj_scroll_by))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = lv_obj_scroll_by &rArr; lv_obj_scroll_by_raw &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_vertical_resolution
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_horizontal_resolution
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_y
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_x
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_delete
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_start
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_var
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_values
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_path_cb
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_exec_cb
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_duration
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_deleted_cb
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_init
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_speed_clamped
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_raw
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_readjust_scroll
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_bounded
</UL>

<P><STRONG><a name="[214]"></a>lv_obj_scroll_by_bounded</STRONG> (Thumb, 220 bytes, Stack size 40 bytes, lv_obj_scroll.o(i.lv_obj_scroll_by_bounded))
<BR><BR>[Stack]<UL><LI>Max Depth = 728<LI>Call Chain = lv_obj_scroll_by_bounded &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_y
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_x
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_top
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_right
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layout
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_bottom
</UL>
<BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_y
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_x
</UL>

<P><STRONG><a name="[213]"></a>lv_obj_scroll_by_raw</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, lv_obj_scroll.o(i.lv_obj_scroll_by_raw))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = lv_obj_scroll_by_raw &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_allocate_spec_attr
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_children_by
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_y_anim
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_x_anim
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
</UL>

<P><STRONG><a name="[1eb]"></a>lv_obj_scroll_to_view_recursive</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, lv_obj_scroll.o(i.lv_obj_scroll_to_view_recursive))
<BR><BR>[Stack]<UL><LI>Max Depth = 720<LI>Call Chain = lv_obj_scroll_to_view_recursive &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layout
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_area_into_view
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[1ea]"></a>lv_obj_scroll_to_x</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, lv_obj_scroll.o(i.lv_obj_scroll_to_x))
<BR><BR>[Stack]<UL><LI>Max Depth = 744<LI>Call Chain = lv_obj_scroll_to_x &rArr; lv_obj_scroll_by_bounded &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_x
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_delete
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_bounded
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[1e9]"></a>lv_obj_scroll_to_y</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, lv_obj_scroll.o(i.lv_obj_scroll_to_y))
<BR><BR>[Stack]<UL><LI>Max Depth = 744<LI>Call Chain = lv_obj_scroll_to_y &rArr; lv_obj_scroll_by_bounded &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_y
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_delete
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_bounded
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[1df]"></a>lv_obj_scrollbar_invalidate</STRONG> (Thumb, 72 bytes, Stack size 40 bytes, lv_obj_scroll.o(i.lv_obj_scrollbar_invalidate))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_scrollbar_invalidate &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_size
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_delete
</UL>

<P><STRONG><a name="[12d]"></a>lv_obj_send_event</STRONG> (Thumb, 72 bytes, Stack size 40 bytes, lv_obj_event.o(i.lv_obj_send_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_push
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_pop
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send_core
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_remove_obj
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;focus_next_core
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scroll_end_cb
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_self_width
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_self_height
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;refresh_children_style
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_raw
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_delete
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scr_load_internal
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scr_load_anim_start
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scr_anim_completed
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_finalize_task_creation
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;indev_reset_core
</UL>

<P><STRONG><a name="[218]"></a>lv_obj_set_height</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_set_height))
<BR><BR>[Stack]<UL><LI>Max Depth = 560<LI>Call Chain = lv_obj_set_height &rArr; lv_obj_set_style_height &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_height
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_size
</UL>

<P><STRONG><a name="[21a]"></a>lv_obj_set_local_style_prop</STRONG> (Thumb, 84 bytes, Stack size 40 bytes, lv_obj_style.o(i.lv_obj_set_local_style_prop))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_set_prop
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_prop_lookup_flags
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_delete
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_local_style
</UL>
<BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_image_recolor_opa
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_image_opa
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_bg_opa
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_y
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_x
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_width
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_height
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_opa
</UL>

<P><STRONG><a name="[21c]"></a>lv_obj_set_pos</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_set_pos))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = lv_obj_set_pos &rArr; lv_obj_set_y &rArr; lv_obj_set_style_y &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_y
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_x
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
</UL>

<P><STRONG><a name="[21f]"></a>lv_obj_set_scrollbar_mode</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, lv_obj_scroll.o(i.lv_obj_set_scrollbar_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = lv_obj_set_scrollbar_mode &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_allocate_spec_attr
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
</UL>

<P><STRONG><a name="[220]"></a>lv_obj_set_size</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_set_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = lv_obj_set_size &rArr; lv_obj_set_width &rArr; lv_obj_set_style_width &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_width
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_height
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
</UL>

<P><STRONG><a name="[222]"></a>lv_obj_set_style_bg_opa</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_obj_style_gen.o(i.lv_obj_set_style_bg_opa))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_set_style_bg_opa &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
</UL>

<P><STRONG><a name="[219]"></a>lv_obj_set_style_height</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_obj_style_gen.o(i.lv_obj_set_style_height))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_set_style_height &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_height
</UL>

<P><STRONG><a name="[223]"></a>lv_obj_set_style_image_opa</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_obj_style_gen.o(i.lv_obj_set_style_image_opa))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_set_style_image_opa &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
</UL>

<P><STRONG><a name="[224]"></a>lv_obj_set_style_image_recolor_opa</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_obj_style_gen.o(i.lv_obj_set_style_image_recolor_opa))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_set_style_image_recolor_opa &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
</UL>

<P><STRONG><a name="[225]"></a>lv_obj_set_style_opa</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_obj_style_gen.o(i.lv_obj_set_style_opa))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_set_style_opa &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;opa_scale_anim
</UL>

<P><STRONG><a name="[226]"></a>lv_obj_set_style_width</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_obj_style_gen.o(i.lv_obj_set_style_width))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_set_style_width &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_width
</UL>

<P><STRONG><a name="[227]"></a>lv_obj_set_style_x</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_obj_style_gen.o(i.lv_obj_set_style_x))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_set_style_x &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_x
</UL>

<P><STRONG><a name="[228]"></a>lv_obj_set_style_y</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_obj_style_gen.o(i.lv_obj_set_style_y))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = lv_obj_set_style_y &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_y
</UL>

<P><STRONG><a name="[221]"></a>lv_obj_set_width</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_set_width))
<BR><BR>[Stack]<UL><LI>Max Depth = 560<LI>Call Chain = lv_obj_set_width &rArr; lv_obj_set_style_width &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_width
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_size
</UL>

<P><STRONG><a name="[21d]"></a>lv_obj_set_x</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_set_x))
<BR><BR>[Stack]<UL><LI>Max Depth = 560<LI>Call Chain = lv_obj_set_x &rArr; lv_obj_set_style_x &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_x
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_pos
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_x_anim
</UL>

<P><STRONG><a name="[21e]"></a>lv_obj_set_y</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_set_y))
<BR><BR>[Stack]<UL><LI>Max Depth = 560<LI>Call Chain = lv_obj_set_y &rArr; lv_obj_set_style_y &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_y
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_local_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_pos
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_y_anim
</UL>

<P><STRONG><a name="[1f9]"></a>lv_obj_style_apply_color_filter</STRONG> (Thumb, 8 bytes, Stack size 12 bytes, lv_obj_style.o(i.lv_obj_style_apply_color_filter))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lv_obj_style_apply_color_filter
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_rect_dsc
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_label_dsc
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_image_dsc
</UL>

<P><STRONG><a name="[141]"></a>lv_obj_style_apply_recolor</STRONG> (Thumb, 64 bytes, Stack size 40 bytes, lv_obj_style.o(i.lv_obj_style_apply_recolor))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = lv_obj_style_apply_recolor &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_to_32
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_over32
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_recolor_recursive
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;normal_apply_layer_recolor
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_apply_layer_recolor
</UL>

<P><STRONG><a name="[229]"></a>lv_obj_style_create_transition</STRONG> (Thumb, 348 bytes, Stack size 136 bytes, lv_obj_style.o(i.lv_obj_style_create_transition))
<BR><BR>[Stack]<UL><LI>Max Depth = 640<LI>Call Chain = lv_obj_style_create_transition &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_ins_head
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_start
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_var
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_values
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_start_cb
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_path_cb
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_exec_cb
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_duration
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_delay
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_completed_cb
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_init
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_set_prop
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_eq
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_user_data
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_early_apply
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_trans_style
</UL>
<BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_obj_state
</UL>

<P><STRONG><a name="[22f]"></a>lv_obj_style_state_compare</STRONG> (Thumb, 596 bytes, Stack size 40 bytes, lv_obj_style.o(i.lv_obj_style_state_compare))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = lv_obj_style_state_compare &rArr; lv_style_get_prop
</UL>
<BR>[Calls]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_get_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_obj_state
</UL>

<P><STRONG><a name="[1f8]"></a>lv_obj_transform_point_array</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, lv_obj_pos.o(i.lv_obj_transform_point_array))
<BR><BR>[Stack]<UL><LI>Max Depth = 196 + In Cycle
<LI>Call Chain = lv_obj_transform_point_array &rArr;  lv_obj_transform_point_array (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_layer_type
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_transform_point_array
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;transform_point_array
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_transform_point_array
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_transformed_area
</UL>

<P><STRONG><a name="[203]"></a>lv_obj_update_layer_type</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, lv_obj_style.o(i.lv_obj_update_layer_type))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = lv_obj_update_layer_type &rArr; lv_obj_allocate_spec_attr &rArr; lv_malloc_zeroed &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_allocate_spec_attr
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_layer_type
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_obj_state
</UL>

<P><STRONG><a name="[1bc]"></a>lv_obj_update_layout</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, lv_obj_pos.o(i.lv_obj_update_layout))
<BR><BR>[Stack]<UL><LI>Max Depth = 688<LI>Call Chain = lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_screen
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;layout_update_core
</UL>
<BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_view_recursive
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_rotation
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_bounded
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_align
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scale_update
</UL>

<P><STRONG><a name="[192]"></a>lv_palette_main</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lv_palette.o(i.lv_palette_main))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = lv_palette_main &rArr; lv_color_black &rArr; lv_color_make
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_black
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_label_dsc_init
</UL>

<P><STRONG><a name="[1ba]"></a>lv_pct_to_px</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, lv_area.o(i.lv_pct_to_px))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_get_pivot
</UL>

<P><STRONG><a name="[232]"></a>lv_point_array_transform</STRONG> (Thumb, 454 bytes, Stack size 48 bytes, lv_area.o(i.lv_point_array_transform))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = lv_point_array_transform
</UL>
<BR>[Calls]<UL><LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_trigo_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;transform_point_array
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_point_transform
</UL>

<P><STRONG><a name="[163]"></a>lv_point_set</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, lv_area.o(i.lv_point_set))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_in
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_constructor
</UL>

<P><STRONG><a name="[1ad]"></a>lv_point_transform</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, lv_area.o(i.lv_point_transform))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = lv_point_transform &rArr; lv_point_array_transform
</UL>
<BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_point_array_transform
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_buf_get_transformed_area
</UL>

<P><STRONG><a name="[138]"></a>lv_realloc</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, lv_mem.o(i.lv_realloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc_core
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_create_obj
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_set_prop
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_trans_style
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_local_style
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_array_resize
</UL>

<P><STRONG><a name="[234]"></a>lv_realloc_core</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, lv_mem_core_builtin.o(i.lv_realloc_core))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_block_size
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc
</UL>

<P><STRONG><a name="[236]"></a>lv_screen_load</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, lv_display.o(i.lv_screen_load))
<BR><BR>[Stack]<UL><LI>Max Depth = 808<LI>Call Chain = lv_screen_load &rArr; lv_screen_load_anim &rArr; lv_obj_set_pos &rArr; lv_obj_set_y &rArr; lv_obj_set_style_y &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
</UL>
<BR>[Called By]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_ui
</UL>

<P><STRONG><a name="[237]"></a>lv_screen_load_anim</STRONG> (Thumb, 862 bytes, Stack size 224 bytes, lv_display.o(i.lv_screen_load_anim))
<BR><BR>[Stack]<UL><LI>Max Depth = 800<LI>Call Chain = lv_screen_load_anim &rArr; lv_obj_set_pos &rArr; lv_obj_set_y &rArr; lv_obj_set_style_y &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_vertical_resolution
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_horizontal_resolution
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_delete
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_pos
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_start
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_var
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_values
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_start_cb
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_exec_cb
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_duration
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_delay
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_set_completed_cb
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_init
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_local_style_prop
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_delete
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scr_load_internal
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_out_anim
</UL>
<BR>[Called By]<UL><LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load
</UL>

<P><STRONG><a name="[14d]"></a>lv_strdup</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, lv_string_builtin.o(i.lv_strdup))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lv_strdup &rArr; lv_malloc &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memcpy
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
</UL>

<P><STRONG><a name="[23a]"></a>lv_strlen</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_string_builtin.o(i.lv_strlen))
<BR><BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_strdup
</UL>

<P><STRONG><a name="[191]"></a>lv_strndup</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, lv_string_builtin.o(i.lv_strndup))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lv_strndup &rArr; lv_malloc &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memcpy
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_strnlen
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_label
</UL>

<P><STRONG><a name="[23b]"></a>lv_strnlen</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_string_builtin.o(i.lv_strnlen))
<BR><BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_strndup
</UL>

<P><STRONG><a name="[1f4]"></a>lv_style_get_prop</STRONG> (Thumb, 114 bytes, Stack size 12 bytes, lv_style.o(i.lv_style_get_prop))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lv_style_get_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_state_compare
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_local_style_prop
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_cb
</UL>

<P><STRONG><a name="[13b]"></a>lv_style_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_style.o(i.lv_style_init))
<BR><BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_trans_style
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_local_style
</UL>

<P><STRONG><a name="[255]"></a>lv_style_is_empty</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lv_style.o(i.lv_style_is_empty))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_completed_cb
</UL>

<P><STRONG><a name="[1f7]"></a>lv_style_prop_get_default</STRONG> (Thumb, 282 bytes, Stack size 44 bytes, lv_style.o(i.lv_style_prop_get_default))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = lv_style_prop_get_default
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
</UL>

<P><STRONG><a name="[202]"></a>lv_style_prop_lookup_flags</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, lv_style.o(i.lv_style_prop_lookup_flags))
<BR><BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;style_has_flag
</UL>

<P><STRONG><a name="[206]"></a>lv_style_remove_prop</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, lv_style.o(i.lv_style_remove_prop))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = lv_style_remove_prop &rArr; lv_malloc &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_local_style_prop
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_delete
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_completed_cb
</UL>

<P><STRONG><a name="[20b]"></a>lv_style_reset</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, lv_style.o(i.lv_style_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = lv_style_reset &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style
</UL>

<P><STRONG><a name="[21b]"></a>lv_style_set_prop</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, lv_style.o(i.lv_style_set_prop))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = lv_style_set_prop &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_start_cb
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_cb
</UL>

<P><STRONG><a name="[241]"></a>lv_text_encoded_letter_next_2</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, lv_text.o(i.lv_text_encoded_letter_next_2))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = lv_text_encoded_letter_next_2
</UL>
<BR>[Called By]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_width_with_flags
</UL>

<P><STRONG><a name="[23c]"></a>lv_text_get_next_line</STRONG> (Thumb, 290 bytes, Stack size 80 bytes, lv_text.o(i.lv_text_get_next_line))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = lv_text_get_next_line &rArr; lv_text_get_next_word &rArr; lv_font_get_glyph_width &rArr; lv_font_get_glyph_dsc
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_font_get_glyph_width
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_next_word
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_size
</UL>

<P><STRONG><a name="[11b]"></a>lv_text_get_size</STRONG> (Thumb, 210 bytes, Stack size 72 bytes, lv_text.o(i.lv_text_get_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = lv_text_get_size &rArr; lv_text_get_next_line &rArr; lv_text_get_next_word &rArr; lv_font_get_glyph_width &rArr; lv_font_get_glyph_dsc
</UL>
<BR>[Calls]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_width_with_flags
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_next_line
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_font_get_line_height
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
</UL>

<P><STRONG><a name="[240]"></a>lv_text_get_width_with_flags</STRONG> (Thumb, 138 bytes, Stack size 48 bytes, lv_text.o(i.lv_text_get_width_with_flags))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = lv_text_get_width_with_flags &rArr; lv_font_get_glyph_width &rArr; lv_font_get_glyph_dsc
</UL>
<BR>[Calls]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_encoded_letter_next_2
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_font_get_glyph_width
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_is_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_size
</UL>

<P><STRONG><a name="[23e]"></a>lv_text_is_cmd</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, lv_text.o(i.lv_text_is_cmd))
<BR><BR>[Called By]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_width_with_flags
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_next_word
</UL>

<P><STRONG><a name="[1d5]"></a>lv_theme_apply</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, lv_theme.o(i.lv_theme_apply))
<BR><BR>[Stack]<UL><LI>Max Depth = 560<LI>Call Chain = lv_theme_apply &rArr; lv_obj_remove_style_all &rArr; lv_obj_remove_style &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style_all
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_theme_get_from_obj
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_theme_recursion
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_bottom_layer
</UL>

<P><STRONG><a name="[242]"></a>lv_theme_get_from_obj</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lv_theme.o(i.lv_theme_get_from_obj))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = lv_theme_get_from_obj &rArr; lv_obj_get_display &rArr; lv_obj_get_screen
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_default
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_get_theme
</UL>
<BR>[Called By]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_theme_apply
</UL>

<P><STRONG><a name="[161]"></a>lv_tick_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, lv_tick.o(i.lv_tick_get))
<BR><BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_start
</UL>

<P><STRONG><a name="[168]"></a>lv_timer_delete</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, lv_timer.o(i.lv_timer_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = lv_timer_delete &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_async_call_cancel
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_async_timer_cb
</UL>

<P><STRONG><a name="[167]"></a>lv_timer_get_next</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, lv_timer.o(i.lv_timer_get_next))
<BR><BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_next
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_async_call_cancel
</UL>

<P><STRONG><a name="[c2]"></a>lv_timer_pause</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lv_timer.o(i.lv_timer_pause))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anim_mark_list_change
</UL>

<P><STRONG><a name="[c1]"></a>lv_timer_resume</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lv_timer.o(i.lv_timer_resume))
<BR><BR>[Calls]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_timer_handler_resume
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anim_mark_list_change
</UL>

<P><STRONG><a name="[19f]"></a>lv_tlsf_block_size</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, lv_tlsf.o(i.lv_tlsf_block_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_tlsf_block_size
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_size
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_from_ptr
</UL>
<BR>[Called By]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc_core
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_core
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free_core
</UL>

<P><STRONG><a name="[1a0]"></a>lv_tlsf_free</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, lv_tlsf.o(i.lv_tlsf_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_prev
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_next
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_mark_as_free
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_free
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_insert
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_from_ptr
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free_core
</UL>

<P><STRONG><a name="[1c7]"></a>lv_tlsf_malloc</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, lv_tlsf.o(i.lv_tlsf_malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_prepare_used
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_locate_free
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adjust_request_size
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_core
</UL>

<P><STRONG><a name="[235]"></a>lv_tlsf_realloc</STRONG> (Thumb, 200 bytes, Stack size 40 bytes, lv_tlsf.o(i.lv_tlsf_realloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memcpy
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_malloc
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_free
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_used
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_size
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_next
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_next
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_mark_as_used
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_free
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_from_ptr
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adjust_request_size
</UL>
<BR>[Called By]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc_core
</UL>

<P><STRONG><a name="[233]"></a>lv_trigo_sin</STRONG> (Thumb, 120 bytes, Stack size 0 bytes, lv_math.o(i.lv_trigo_sin))
<BR><BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_point_array_transform
</UL>

<P><STRONG><a name="[130]"></a>lv_utils_bsearch</STRONG> (Thumb, 76 bytes, Stack size 40 bytes, lv_utils.o(i.lv_utils_bsearch))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = lv_utils_bsearch
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_kern_value
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_glyph_dsc_id
</UL>

<P><STRONG><a name="[64]"></a>main</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 880<LI>Call Chain = main &rArr; setup_ui &rArr; setup_scr_test1 &rArr; lv_image_set_src &rArr; update_align &rArr; lv_image_set_pivot &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_ui
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_LTDC_Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FMC_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA2D_Init
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[251]"></a>setup_bottom_layer</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, gui_guider.o(i.setup_bottom_layer))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = setup_bottom_layer &rArr; lv_theme_apply &rArr; lv_obj_remove_style_all &rArr; lv_obj_remove_style &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_theme_apply
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_layer_bottom
</UL>
<BR>[Called By]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_ui
</UL>

<P><STRONG><a name="[252]"></a>setup_scr_test1</STRONG> (Thumb, 136 bytes, Stack size 8 bytes, setup_scr_test1.o(i.setup_scr_test1))
<BR><BR>[Stack]<UL><LI>Max Depth = 848<LI>Call Chain = setup_scr_test1 &rArr; lv_image_set_src &rArr; update_align &rArr; lv_image_set_pivot &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layout
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_image_recolor_opa
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_image_opa
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_bg_opa
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_size
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_scrollbar_mode
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_pos
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_create
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_rotation
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_create
</UL>
<BR>[Called By]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_ui
</UL>

<P><STRONG><a name="[245]"></a>setup_ui</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, gui_guider.o(i.setup_ui))
<BR><BR>[Stack]<UL><LI>Max Depth = 856<LI>Call Chain = setup_ui &rArr; setup_scr_test1 &rArr; lv_image_set_src &rArr; update_align &rArr; lv_image_set_pivot &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_scr_test1
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setup_bottom_layer
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_scr_del_flag
<LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_keyboard
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[94]"></a>HAL_FMC_MspInit</STRONG> (Thumb, 162 bytes, Stack size 48 bytes, fmc.o(i.HAL_FMC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_FMC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SDRAM_MspInit
</UL>

<P><STRONG><a name="[9d]"></a>LTDC_SetConfig</STRONG> (Thumb, 424 bytes, Stack size 12 bytes, stm32f4xx_hal_ltdc.o(i.LTDC_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = LTDC_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_ConfigLayer
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_SetWindowSize
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_LTDC_SetWindowPosition
</UL>

<P><STRONG><a name="[12a]"></a>focus_next_core</STRONG> (Thumb, 290 bytes, Stack size 40 bytes, lv_group.o(i.focus_next_core))
<BR><BR>[Stack]<UL><LI>Max Depth = 480<LI>Call Chain = focus_next_core &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_state
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_indev
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_focus_prev
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_focus_next
</UL>

<P><STRONG><a name="[12c]"></a>get_indev</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, lv_group.o(i.get_indev))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = get_indev
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_type
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_next
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_group
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_remove_obj
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;focus_next_core
</UL>

<P><STRONG><a name="[1a8]"></a>lv_group_refocus</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, lv_group.o(i.lv_group_refocus))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = lv_group_refocus &rArr; lv_group_focus_prev &rArr; focus_next_core &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_focus_prev
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_focus_next
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_remove_obj
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_add_obj
</UL>

<P><STRONG><a name="[11d]"></a>draw_scrollbar</STRONG> (Thumb, 98 bytes, Stack size 160 bytes, lv_obj.o(i.draw_scrollbar))
<BR><BR>[Stack]<UL><LI>Max Depth = 664<LI>Call Chain = draw_scrollbar &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_size
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scrollbar_init_draw_dsc
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
</UL>

<P><STRONG><a name="[7d]"></a>lv_obj_constructor</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, lv_obj.o(i.lv_obj_constructor))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = lv_obj_constructor &rArr; lv_obj_get_scroll_left &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_top
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_obj.o(.constdata)
</UL>
<P><STRONG><a name="[7e]"></a>lv_obj_destructor</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, lv_obj.o(i.lv_obj_destructor))
<BR><BR>[Stack]<UL><LI>Max Depth = 560<LI>Call Chain = lv_obj_destructor &rArr; lv_obj_remove_style_all &rArr; lv_obj_remove_style &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style_all
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_enable_style_refresh
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_remove_all
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_mark_deleted
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_delete
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_group
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_remove_obj
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_obj.o(.constdata)
</UL>
<P><STRONG><a name="[1e2]"></a>lv_obj_draw</STRONG> (Thumb, 620 bytes, Stack size 160 bytes, lv_obj.o(i.lv_obj_draw))
<BR><BR>[Stack]<UL><LI>Max Depth = 824<LI>Call Chain = lv_obj_draw &rArr; draw_scrollbar &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_rect_dsc
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_param
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_layer
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_current_target
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect_dsc_init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_in
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_increase
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_scrollbar
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_code
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event
</UL>

<P><STRONG><a name="[7f]"></a>lv_obj_event</STRONG> (Thumb, 1124 bytes, Stack size 56 bytes, lv_obj.o(i.lv_obj_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 880<LI>Call Chain = lv_obj_event &rArr; lv_obj_draw &rArr; draw_scrollbar &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_y
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_x
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_view_recursive
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_mark_layout_as_dirty
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_is_editable
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_mode
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scrollbar_area
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_y
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_x
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_right
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_dir
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_child_count
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_calculate_ext_draw_size
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_scroll_obj
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_active
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_set_ext_draw_size
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_param
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_key
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_indev
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_current_target
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_state
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_state
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_draw
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_state
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_group
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_type
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_group_get_editing
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_code
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_obj.o(.constdata)
</UL>
<P><STRONG><a name="[120]"></a>scrollbar_init_draw_dsc</STRONG> (Thumb, 328 bytes, Stack size 24 bytes, lv_obj.o(i.scrollbar_init_draw_dsc))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = scrollbar_init_draw_dsc &rArr; lv_obj_get_style_opa_recursive &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_opa_recursive
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_rect_dsc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_scrollbar
</UL>

<P><STRONG><a name="[1cc]"></a>update_obj_state</STRONG> (Thumb, 496 bytes, Stack size 56 bytes, lv_obj.o(i.update_obj_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 696<LI>Call Chain = update_obj_state &rArr; lv_obj_style_create_transition &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layer_type
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_state_compare
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_zeroed
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_state
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_state
</UL>

<P><STRONG><a name="[1d3]"></a>get_instance_size</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, lv_obj_class.o(i.get_instance_size))
<BR><BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_create_obj
</UL>

<P><STRONG><a name="[1d6]"></a>lv_obj_construct</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, lv_obj_class.o(i.lv_obj_construct))
<BR><BR>[Stack]<UL><LI>Max Depth = 16 + In Cycle
<LI>Call Chain = lv_obj_construct &rArr;  lv_obj_construct (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_construct
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_construct
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_class_init_obj
</UL>

<P><STRONG><a name="[135]"></a>get_layer_opa</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, lv_obj_draw.o(i.get_layer_opa))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = get_layer_opa &rArr; lv_obj_get_style_opa_recursive &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_opa_recursive
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_rect_dsc
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_label_dsc
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_image_dsc
</UL>

<P><STRONG><a name="[140]"></a>image_apply_layer_recolor</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, lv_obj_draw.o(i.image_apply_layer_recolor))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = image_apply_layer_recolor &rArr; lv_obj_get_style_recolor_recursive &rArr; lv_obj_style_apply_recolor &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_apply_recolor
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_recolor_recursive
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_to_32
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_over32
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_rect_dsc
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_image_dsc
</UL>

<P><STRONG><a name="[1fa]"></a>normal_apply_layer_recolor</STRONG> (Thumb, 78 bytes, Stack size 32 bytes, lv_obj_draw.o(i.normal_apply_layer_recolor))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = normal_apply_layer_recolor &rArr; lv_obj_get_style_recolor_recursive &rArr; lv_obj_style_apply_recolor &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_apply_recolor
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_recolor_recursive
<LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_mix
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_make
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_rect_dsc
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_label_dsc
</UL>

<P><STRONG><a name="[123]"></a>event_is_bubbled</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, lv_obj_event.o(i.event_is_bubbled))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = event_is_bubbled
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send_core
</UL>

<P><STRONG><a name="[125]"></a>event_send_core</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, lv_obj_event.o(i.event_send_core))
<BR><BR>[Stack]<UL><LI>Max Depth = 304 + In Cycle
<LI>Call Chain = event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_active
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_send
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event_base
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send_core
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_is_bubbled
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send_core
</UL>

<P><STRONG><a name="[ea]"></a>calc_content_height</STRONG> (Thumb, 286 bytes, Stack size 48 bytes, lv_obj_pos.o(i.calc_content_height))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = calc_content_height &rArr; lv_obj_get_self_height &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_is_layout_positioned
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_y
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_child_count
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag_any
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_height
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_self_height
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_top
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_bottom
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
</UL>

<P><STRONG><a name="[f4]"></a>calc_content_width</STRONG> (Thumb, 500 bytes, Stack size 48 bytes, lv_obj_pos.o(i.calc_content_width))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = calc_content_width &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_is_layout_positioned
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_x
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_child_count
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag_any
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_width
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_self_width
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_right
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_space_left
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
</UL>

<P><STRONG><a name="[1d0]"></a>is_transformed</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, lv_obj_pos.o(i.is_transformed))
<BR><BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_area_is_visible
</UL>

<P><STRONG><a name="[152]"></a>layout_update_core</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, lv_obj_pos.o(i.layout_update_core))
<BR><BR>[Stack]<UL><LI>Max Depth = 672 + In Cycle
<LI>Call Chain = layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_child_count
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_readjust_scroll
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_layout_apply
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_pos
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;layout_update_core
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layout
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;layout_update_core
</UL>

<P><STRONG><a name="[ed]"></a>lv_obj_get_style_space_bottom</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, lv_obj_pos.o(i.lv_obj_get_style_space_bottom))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = lv_obj_get_style_space_bottom &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_height
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_coords
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_height
</UL>

<P><STRONG><a name="[f7]"></a>lv_obj_get_style_space_left</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, lv_obj_pos.o(i.lv_obj_get_style_space_left))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = lv_obj_get_style_space_left &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_width
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_coords
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_width
</UL>

<P><STRONG><a name="[f6]"></a>lv_obj_get_style_space_right</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, lv_obj_pos.o(i.lv_obj_get_style_space_right))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = lv_obj_get_style_space_right &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_width
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_coords
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_width
</UL>

<P><STRONG><a name="[ec]"></a>lv_obj_get_style_space_top</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, lv_obj_pos.o(i.lv_obj_get_style_space_top))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = lv_obj_get_style_space_top &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refr_size
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_move_to
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_height
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_content_coords
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_content_height
</UL>

<P><STRONG><a name="[231]"></a>transform_point_array</STRONG> (Thumb, 318 bytes, Stack size 80 bytes, lv_obj_pos.o(i.transform_point_array))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = transform_point_array &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_width
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_height
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_point_array_transform
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_transform_point_array
</UL>

<P><STRONG><a name="[215]"></a>scroll_area_into_view</STRONG> (Thumb, 800 bytes, Stack size 80 bytes, lv_obj_scroll.o(i.scroll_area_into_view))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = scroll_area_into_view &rArr; lv_obj_scroll_by &rArr; lv_obj_scroll_by_raw &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_top
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_right
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_left
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_dir
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_delete
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_has_flag
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_parent
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_width
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_height
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_snap_y
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_snap_x
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_bottom
</UL>
<BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_to_view_recursive
</UL>

<P><STRONG><a name="[70]"></a>scroll_end_cb</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, lv_obj_scroll.o(i.scroll_end_cb))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = scroll_end_cb &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_obj_scroll.o(i.lv_obj_scroll_by)
</UL>
<P><STRONG><a name="[71]"></a>scroll_x_anim</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lv_obj_scroll.o(i.scroll_x_anim))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = scroll_x_anim &rArr; lv_obj_scroll_by_raw &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_x
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_raw
</UL>
<BR>[Address Reference Count : 3]<UL><LI> lv_obj_scroll.o(i.lv_obj_scroll_by)
<LI> lv_obj_scroll.o(i.lv_obj_scroll_to_x)
<LI> lv_obj_scroll.o(i.scroll_area_into_view)
</UL>
<P><STRONG><a name="[73]"></a>scroll_y_anim</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lv_obj_scroll.o(i.scroll_y_anim))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = scroll_y_anim &rArr; lv_obj_scroll_by_raw &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_scroll_y
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_scroll_by_raw
</UL>
<BR>[Address Reference Count : 3]<UL><LI> lv_obj_scroll.o(i.lv_obj_scroll_by)
<LI> lv_obj_scroll.o(i.lv_obj_scroll_to_y)
<LI> lv_obj_scroll.o(i.scroll_area_into_view)
</UL>
<P><STRONG><a name="[fa]"></a>calculate_layer_type</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, lv_obj_style.o(i.calculate_layer_type))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = calculate_layer_type &rArr; lv_obj_get_style_prop &rArr; get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layer_type
</UL>

<P><STRONG><a name="[207]"></a>full_cache_refresh</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, lv_obj_style.o(i.full_cache_refresh))
<BR><BR>[Called By]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_local_style_prop
</UL>

<P><STRONG><a name="[137]"></a>get_local_style</STRONG> (Thumb, 212 bytes, Stack size 24 bytes, lv_obj_style.o(i.get_local_style))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = get_local_style &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc_zeroed
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_init
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
</UL>

<P><STRONG><a name="[13d]"></a>get_prop_core</STRONG> (Thumb, 500 bytes, Stack size 44 bytes, lv_obj_style.o(i.get_prop_core))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = get_prop_core
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_selector_style_prop
</UL>

<P><STRONG><a name="[13c]"></a>get_selector_style_prop</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, lv_obj_style.o(i.get_selector_style_prop))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = get_selector_style_prop &rArr; get_prop_core
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_prop_core
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
</UL>

<P><STRONG><a name="[13e]"></a>get_trans_style</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, lv_obj_style.o(i.get_trans_style))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = get_trans_style &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_init
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_style_create_transition
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_start_cb
</UL>

<P><STRONG><a name="[204]"></a>refresh_children_style</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, lv_obj_style.o(i.refresh_children_style))
<BR><BR>[Stack]<UL><LI>Max Depth = 464 + In Cycle
<LI>Call Chain = refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_child_count
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;refresh_children_style
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;refresh_children_style
</UL>

<P><STRONG><a name="[209]"></a>style_has_flag</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, lv_obj_style.o(i.style_has_flag))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = style_has_flag
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_prop_lookup_flags
</UL>
<BR>[Called By]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style
</UL>

<P><STRONG><a name="[74]"></a>trans_anim_cb</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, lv_obj_style.o(i.trans_anim_cb))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = trans_anim_cb &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_mix
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_set_prop
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_get_prop
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_eq
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_obj_style.o(i.lv_obj_style_create_transition)
</UL>
<P><STRONG><a name="[76]"></a>trans_anim_completed_cb</STRONG> (Thumb, 190 bytes, Stack size 24 bytes, lv_obj_style.o(i.trans_anim_completed_cb))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = trans_anim_completed_cb &rArr; lv_obj_remove_style &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_next
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_remove_prop
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_is_empty
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_obj_style.o(i.lv_obj_style_create_transition)
</UL>
<P><STRONG><a name="[75]"></a>trans_anim_start_cb</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, lv_obj_style.o(i.trans_anim_start_cb))
<BR><BR>[Stack]<UL><LI>Max Depth = 520<LI>Call Chain = trans_anim_start_cb &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_style
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_set_prop
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_delete
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_trans_style
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_obj_style.o(i.lv_obj_style_create_transition)
</UL>
<P><STRONG><a name="[20a]"></a>trans_delete</STRONG> (Thumb, 182 bytes, Stack size 40 bytes, lv_obj_style.o(i.trans_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = trans_delete &rArr; lv_style_remove_prop &rArr; lv_malloc &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_delete
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_tail
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_prev
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_style_remove_prop
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_local_style_prop
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_style
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trans_anim_start_cb
</UL>

<P><STRONG><a name="[7c]"></a>lv_obj_delete_async_cb</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_obj_tree.o(i.lv_obj_delete_async_cb))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = lv_obj_delete_async_cb &rArr; lv_obj_delete &rArr; lv_obj_scrollbar_invalidate &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_delete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_obj_tree.o(i.obj_delete_core)
</UL>
<P><STRONG><a name="[1de]"></a>obj_delete_core</STRONG> (Thumb, 406 bytes, Stack size 24 bytes, lv_obj_tree.o(i.obj_delete_core))
<BR><BR>[Stack]<UL><LI>Max Depth = 424 + In Cycle
<LI>Call Chain = obj_delete_core &rArr;  obj_delete_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_realloc
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_destruct
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_remove_all
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_group
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_type
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_next
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_active_obj
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_async_call_cancel
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_index
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_child
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_indev_reset
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_delete
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>

<P><STRONG><a name="[249]"></a>obj_indev_reset</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, lv_obj_tree.o(i.obj_indev_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = obj_indev_reset &rArr; lv_indev_reset &rArr; indev_reset_core &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_wait_release
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_reset
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_get_state
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;obj_delete_core
</UL>

<P><STRONG><a name="[239]"></a>is_out_anim</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, lv_display.o(i.is_out_anim))
<BR><BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
</UL>

<P><STRONG><a name="[7b]"></a>opa_scale_anim</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_display.o(i.opa_scale_anim))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = opa_scale_anim &rArr; lv_obj_set_style_opa &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_style_opa
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_display.o(i.lv_screen_load_anim)
</UL>
<P><STRONG><a name="[78]"></a>scr_anim_completed</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, lv_display.o(i.scr_anim_completed))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = scr_anim_completed &rArr; lv_obj_delete &rArr; lv_obj_scrollbar_invalidate &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_local_style_prop
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_delete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_display.o(i.lv_screen_load_anim)
</UL>
<P><STRONG><a name="[77]"></a>scr_load_anim_start</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, lv_display.o(i.scr_load_anim_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = scr_load_anim_start &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_display.o(i.lv_screen_load_anim)
</UL>
<P><STRONG><a name="[238]"></a>scr_load_internal</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, lv_display.o(i.scr_load_internal))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = scr_load_internal &rArr; lv_obj_invalidate &rArr; lv_obj_invalidate_area &rArr; lv_inv_area &rArr; lv_display_send_event &rArr; lv_event_send &rArr; cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_screen_load_anim
</UL>

<P><STRONG><a name="[79]"></a>set_x_anim</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_display.o(i.set_x_anim))
<BR><BR>[Stack]<UL><LI>Max Depth = 560<LI>Call Chain = set_x_anim &rArr; lv_obj_set_x &rArr; lv_obj_set_style_x &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_x
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_display.o(i.lv_screen_load_anim)
</UL>
<P><STRONG><a name="[7a]"></a>set_y_anim</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_display.o(i.set_y_anim))
<BR><BR>[Stack]<UL><LI>Max Depth = 560<LI>Call Chain = set_y_anim &rArr; lv_obj_set_y &rArr; lv_obj_set_style_y &rArr; lv_obj_set_local_style_prop &rArr; lv_obj_refresh_style &rArr; refresh_children_style &rArr;  refresh_children_style (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_set_y
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_display.o(i.lv_screen_load_anim)
</UL>
<P><STRONG><a name="[102]"></a>cleanup_task</STRONG> (Thumb, 150 bytes, Stack size 24 bytes, lv_draw.o(i.cleanup_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = cleanup_task &rArr; lv_draw_buf_destroy &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_height
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_buf_destroy
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_task_get_label_dsc
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_dispatch_layer
</UL>

<P><STRONG><a name="[185]"></a>draw_buf_free</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lv_draw_buf.o(i.draw_buf_free))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_buf_destroy
</UL>

<P><STRONG><a name="[105]"></a>draw_buf_get_full_area</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, lv_draw_buf.o(i.draw_buf_get_full_area))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = draw_buf_get_full_area
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_set
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_buf_flush_cache
</UL>

<P><STRONG><a name="[145]"></a>image_decoder_get_info</STRONG> (Thumb, 338 bytes, Stack size 56 bytes, lv_image_decoder.o(i.image_decoder_get_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = image_decoder_get_info &rArr; lv_fs_open &rArr; lv_malloc_zeroed &rArr; lv_malloc_core &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_next
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_memset
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;img_width_to_stride
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_strdup
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_header_cache_is_enabled
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_seek
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_open
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_close
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_release
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_get_data
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_add
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_acquire
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_open
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_get_info
</UL>

<P><STRONG><a name="[14b]"></a>img_width_to_stride</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, lv_image_decoder.o(i.img_width_to_stride))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = img_width_to_stride
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_format_get_bpp
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;image_decoder_get_info
</UL>

<P><STRONG><a name="[1b5]"></a>try_cache</STRONG> (Thumb, 58 bytes, Stack size 40 bytes, lv_image_decoder.o(i.try_cache))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = try_cache &rArr; lv_cache_acquire &rArr; lv_cache_entry_acquire_data
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_get_data
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_acquire
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_decoder_open
</UL>

<P><STRONG><a name="[12f]"></a>get_glyph_dsc_id</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, lv_font_fmt_txt.o(i.get_glyph_dsc_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = get_glyph_dsc_id &rArr; lv_utils_bsearch
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_utils_bsearch
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_font_get_glyph_dsc_fmt_txt
</UL>

<P><STRONG><a name="[134]"></a>get_kern_value</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, lv_font_fmt_txt.o(i.get_kern_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = get_kern_value &rArr; lv_utils_bsearch
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_utils_bsearch
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_font_get_glyph_dsc_fmt_txt
</UL>

<P><STRONG><a name="[68]"></a>kern_pair_16_compare</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_font_fmt_txt.o(i.kern_pair_16_compare))
<BR>[Address Reference Count : 1]<UL><LI> lv_font_fmt_txt.o(i.get_kern_value)
</UL>
<P><STRONG><a name="[69]"></a>kern_pair_8_compare</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_font_fmt_txt.o(i.kern_pair_8_compare))
<BR>[Address Reference Count : 1]<UL><LI> lv_font_fmt_txt.o(i.get_kern_value)
</UL>
<P><STRONG><a name="[67]"></a>unicode_list_compare</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_font_fmt_txt.o(i.unicode_list_compare))
<BR>[Address Reference Count : 1]<UL><LI> lv_font_fmt_txt.o(i.get_glyph_dsc_id)
</UL>
<P><STRONG><a name="[150]"></a>indev_reset_core</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, lv_indev.o(i.indev_reset_core))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = indev_reset_core &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_send_event
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_send_event
</UL>
<BR>[Called By]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_indev_reset
</UL>

<P><STRONG><a name="[e6]"></a>cache_add_internal_no_lock</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, lv_cache.o(i.cache_add_internal_no_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = cache_add_internal_no_lock &rArr; cache_evict_one_internal_no_lock &rArr; lv_cache_entry_delete &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cache_evict_one_internal_no_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_add
</UL>

<P><STRONG><a name="[e7]"></a>cache_evict_one_internal_no_lock</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, lv_cache.o(i.cache_evict_one_internal_no_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = cache_evict_one_internal_no_lock &rArr; lv_cache_entry_delete &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_get_data
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cache_entry_delete
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cache_add_internal_no_lock
</UL>

<P><STRONG><a name="[c0]"></a>anim_mark_list_change</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lv_anim.o(i.anim_mark_list_change))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = anim_mark_list_change
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_timer_pause
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_timer_resume
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_delete
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_start
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_concurrent_anims
</UL>

<P><STRONG><a name="[15a]"></a>lv_anim_path_cubic_bezier</STRONG> (Thumb, 62 bytes, Stack size 32 bytes, lv_anim.o(i.lv_anim_path_cubic_bezier))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lv_anim_path_cubic_bezier &rArr; lv_cubic_bezier &rArr; __aeabi_ldivmod &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_map
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cubic_bezier
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_path_ease_out
</UL>

<P><STRONG><a name="[158]"></a>remove_anim</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, lv_anim.o(i.remove_anim))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = remove_anim &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_delete
</UL>

<P><STRONG><a name="[15e]"></a>remove_concurrent_anims</STRONG> (Thumb, 150 bytes, Stack size 24 bytes, lv_anim.o(i.remove_concurrent_anims))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = remove_concurrent_anims &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_next
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_get_head
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anim_mark_list_change
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_start
</UL>

<P><STRONG><a name="[162]"></a>resolve_time</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, lv_anim.o(i.resolve_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = resolve_time &rArr; lv_anim_resolve_speed
</UL>
<BR>[Calls]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_resolve_speed
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_anim_start
</UL>

<P><STRONG><a name="[165]"></a>lv_point_within_circle</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, lv_area.o(i.lv_point_within_circle))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_point_on
</UL>

<P><STRONG><a name="[6b]"></a>lv_async_timer_cb</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, lv_async.o(i.lv_async_timer_cb))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = lv_async_timer_cb &rArr; lv_timer_delete &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_timer_delete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_async.o(i.lv_async_call_cancel)
</UL>
<P><STRONG><a name="[fb]"></a>cleanup_event_list</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, lv_event.o(i.cleanup_event_list))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = cleanup_event_list &rArr; cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_event_list_core
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_remove_all
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_send
</UL>

<P><STRONG><a name="[fc]"></a>cleanup_event_list_core</STRONG> (Thumb, 92 bytes, Stack size 32 bytes, lv_event.o(i.cleanup_event_list_core))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = cleanup_event_list_core &rArr; lv_array_resize &rArr; lv_realloc &rArr; lv_realloc_core &rArr; lv_tlsf_realloc &rArr; lv_tlsf_malloc &rArr; block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_array_resize
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_array_deinit
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_array_at
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_is_marked_deleting
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_send
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_event_list
</UL>

<P><STRONG><a name="[122]"></a>event_array_at</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_event.o(i.event_array_at))
<BR><BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_array_at
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_remove_all
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_send
</UL>

<P><STRONG><a name="[199]"></a>event_array_size</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_event.o(i.event_array_size))
<BR><BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_remove_all
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_send
</UL>

<P><STRONG><a name="[fe]"></a>event_is_marked_deleting</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_event.o(i.event_is_marked_deleting))
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_send
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cleanup_event_list_core
</UL>

<P><STRONG><a name="[19a]"></a>event_mark_deleting</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lv_event.o(i.event_mark_deleting))
<BR><BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_remove_all
</UL>

<P><STRONG><a name="[1a2]"></a>lv_fs_resolve_path</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, lv_fs.o(i.lv_fs_resolve_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_fs_resolve_path
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_open
</UL>

<P><STRONG><a name="[1a3]"></a>lv_fs_seek_cached</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, lv_fs.o(i.lv_fs_seek_cached))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lv_fs_seek_cached
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_fs_seek
</UL>

<P><STRONG><a name="[1c5]"></a>node_set_next</STRONG> (Thumb, 20 bytes, Stack size 12 bytes, lv_ll.o(i.node_set_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = node_set_next
</UL>
<BR>[Called By]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_ins_tail
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_ins_head
</UL>

<P><STRONG><a name="[1c4]"></a>node_set_prev</STRONG> (Thumb, 18 bytes, Stack size 12 bytes, lv_ll.o(i.node_set_prev))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = node_set_prev
</UL>
<BR>[Called By]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_remove
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_ins_tail
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_ll_ins_head
</UL>

<P><STRONG><a name="[175]"></a>do_cubic_bezier</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lv_math.o(i.do_cubic_bezier))
<BR><BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_cubic_bezier
</UL>

<P><STRONG><a name="[23d]"></a>lv_text_get_next_word</STRONG> (Thumb, 598 bytes, Stack size 64 bytes, lv_text.o(i.lv_text_get_next_word))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = lv_text_get_next_word &rArr; lv_font_get_glyph_width &rArr; lv_font_get_glyph_dsc
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_font_get_glyph_width
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_is_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_next_line
</UL>

<P><STRONG><a name="[84]"></a>lv_text_utf8_get_byte_id</STRONG> (Thumb, 50 bytes, Stack size 24 bytes, lv_text.o(i.lv_text_utf8_get_byte_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = lv_text_utf8_get_byte_id
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_text.o(.constdata)
</UL>
<P><STRONG><a name="[83]"></a>lv_text_utf8_next</STRONG> (Thumb, 286 bytes, Stack size 8 bytes, lv_text.o(i.lv_text_utf8_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lv_text_utf8_next
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_text.o(.constdata)
</UL>
<P><STRONG><a name="[82]"></a>lv_text_utf8_size</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, lv_text.o(i.lv_text_utf8_size))
<BR>[Address Reference Count : 1]<UL><LI> lv_text.o(.constdata)
</UL>
<P><STRONG><a name="[243]"></a>lv_timer_handler_resume</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, lv_timer.o(i.lv_timer_handler_resume))
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_timer_resume
</UL>

<P><STRONG><a name="[be]"></a>adjust_request_size</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, lv_tlsf.o(i.adjust_request_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = adjust_request_size
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;align_up
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_malloc
</UL>

<P><STRONG><a name="[e3]"></a>align_ptr</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lv_tlsf.o(i.align_ptr))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;insert_free_block
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_split
</UL>

<P><STRONG><a name="[bf]"></a>align_up</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lv_tlsf.o(i.align_up))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adjust_request_size
</UL>

<P><STRONG><a name="[c5]"></a>block_absorb</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, lv_tlsf.o(i.block_absorb))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_size
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_link_next
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_last
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_prev
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_next
</UL>

<P><STRONG><a name="[c9]"></a>block_can_split</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lv_tlsf.o(i.block_can_split))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = block_can_split
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_size
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_used
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_free
</UL>

<P><STRONG><a name="[244]"></a>block_from_ptr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_tlsf.o(i.block_from_ptr))
<BR><BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_free
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_block_size
</UL>

<P><STRONG><a name="[ca]"></a>block_insert</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, lv_tlsf.o(i.block_insert))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = block_insert &rArr; insert_free_block
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mapping_insert
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;insert_free_block
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_size
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_free
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_used
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_free
</UL>

<P><STRONG><a name="[d9]"></a>block_is_free</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_tlsf.o(i.block_is_free))
<BR><BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_free
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_used
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_free
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_prev
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_next
</UL>

<P><STRONG><a name="[c6]"></a>block_is_last</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, lv_tlsf.o(i.block_is_last))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_size
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_next
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_next
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_absorb
</UL>

<P><STRONG><a name="[dc]"></a>block_is_prev_free</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_tlsf.o(i.block_is_prev_free))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_prev
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_prev
</UL>

<P><STRONG><a name="[c8]"></a>block_link_next</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, lv_tlsf.o(i.block_link_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_next
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_free
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_mark_as_free
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_absorb
</UL>

<P><STRONG><a name="[ce]"></a>block_locate_free</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, lv_tlsf.o(i.block_locate_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = block_locate_free &rArr; mapping_search &rArr; mapping_insert
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;search_suitable_block
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_free_block
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mapping_search
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_size
</UL>
<BR>[Called By]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_malloc
</UL>

<P><STRONG><a name="[d2]"></a>block_mark_as_free</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lv_tlsf.o(i.block_mark_as_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_set_prev_free
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_set_free
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_link_next
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_free
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_split
</UL>

<P><STRONG><a name="[d5]"></a>block_mark_as_used</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lv_tlsf.o(i.block_mark_as_used))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = block_mark_as_used &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_set_used
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_set_prev_used
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_next
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_prepare_used
</UL>

<P><STRONG><a name="[d8]"></a>block_merge_next</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, lv_tlsf.o(i.block_merge_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = block_merge_next &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_remove
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_next
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_last
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_free
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_absorb
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_free
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_used
</UL>

<P><STRONG><a name="[db]"></a>block_merge_prev</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, lv_tlsf.o(i.block_merge_prev))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_remove
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_prev
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_prev_free
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_free
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_absorb
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_free
</UL>

<P><STRONG><a name="[cd]"></a>block_next</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, lv_tlsf.o(i.block_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;offset_to_block
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_to_ptr
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_size
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_last
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_next
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_mark_as_used
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_link_next
</UL>

<P><STRONG><a name="[e0]"></a>block_prepare_used</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, lv_tlsf.o(i.block_prepare_used))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = block_prepare_used &rArr; block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_free
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_to_ptr
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_mark_as_used
</UL>
<BR>[Called By]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_malloc
</UL>

<P><STRONG><a name="[dd]"></a>block_prev</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, lv_tlsf.o(i.block_prev))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = block_prev
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_prev_free
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_prev
</UL>

<P><STRONG><a name="[da]"></a>block_remove</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, lv_tlsf.o(i.block_remove))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = block_remove &rArr; mapping_insert
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_free_block
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mapping_insert
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_size
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_prev
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_next
</UL>

<P><STRONG><a name="[d4]"></a>block_set_free</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_tlsf.o(i.block_set_free))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_mark_as_free
</UL>

<P><STRONG><a name="[d3]"></a>block_set_prev_free</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_tlsf.o(i.block_set_prev_free))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_free
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_mark_as_free
</UL>

<P><STRONG><a name="[d6]"></a>block_set_prev_used</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_tlsf.o(i.block_set_prev_used))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_used
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_mark_as_used
</UL>

<P><STRONG><a name="[e4]"></a>block_set_size</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lv_tlsf.o(i.block_set_size))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_split
</UL>

<P><STRONG><a name="[d7]"></a>block_set_used</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lv_tlsf.o(i.block_set_used))
<BR><BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_mark_as_used
</UL>

<P><STRONG><a name="[c7]"></a>block_size</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lv_tlsf.o(i.block_size))
<BR><BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_block_size
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_split
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_remove
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_next
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_locate_free
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_last
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_insert
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_can_split
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_absorb
</UL>

<P><STRONG><a name="[e2]"></a>block_split</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, lv_tlsf.o(i.block_split))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;offset_to_block
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_to_ptr
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_size
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_set_size
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_mark_as_free
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;align_ptr
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_used
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_trim_free
</UL>

<P><STRONG><a name="[de]"></a>block_to_ptr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_tlsf.o(i.block_to_ptr))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;insert_free_block
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_split
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_prepare_used
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_next
</UL>

<P><STRONG><a name="[e1]"></a>block_trim_free</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, lv_tlsf.o(i.block_trim_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = block_trim_free &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_split
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_set_prev_free
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_link_next
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_free
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_insert
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_can_split
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_prepare_used
</UL>

<P><STRONG><a name="[e5]"></a>block_trim_used</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, lv_tlsf.o(i.block_trim_used))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = block_trim_used &rArr; block_split &rArr; block_mark_as_free &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_split
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_set_prev_used
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_merge_next
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_is_free
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_insert
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_can_split
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_tlsf_realloc
</UL>

<P><STRONG><a name="[cc]"></a>insert_free_block</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, lv_tlsf.o(i.insert_free_block))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = insert_free_block
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_to_ptr
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;align_ptr
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_insert
</UL>

<P><STRONG><a name="[cb]"></a>mapping_insert</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, lv_tlsf.o(i.mapping_insert))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = mapping_insert
</UL>
<BR>[Calls]<UL><LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tlsf_fls
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mapping_search
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_remove
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_insert
</UL>

<P><STRONG><a name="[cf]"></a>mapping_search</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, lv_tlsf.o(i.mapping_search))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = mapping_search &rArr; mapping_insert
</UL>
<BR>[Calls]<UL><LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tlsf_fls
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mapping_insert
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_locate_free
</UL>

<P><STRONG><a name="[df]"></a>offset_to_block</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lv_tlsf.o(i.offset_to_block))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_split
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_next
</UL>

<P><STRONG><a name="[d1]"></a>remove_free_block</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, lv_tlsf.o(i.remove_free_block))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = remove_free_block
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_remove
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_locate_free
</UL>

<P><STRONG><a name="[d0]"></a>search_suitable_block</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, lv_tlsf.o(i.search_suitable_block))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = search_suitable_block
</UL>
<BR>[Calls]<UL><LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tlsf_ffs
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;block_locate_free
</UL>

<P><STRONG><a name="[250]"></a>tlsf_ffs</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lv_tlsf.o(i.tlsf_ffs))
<BR><BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;search_suitable_block
</UL>

<P><STRONG><a name="[246]"></a>tlsf_fls</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lv_tlsf.o(i.tlsf_fls))
<BR><BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mapping_search
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mapping_insert
</UL>

<P><STRONG><a name="[c3]"></a>apply_theme</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, lv_theme.o(i.apply_theme))
<BR><BR>[Stack]<UL><LI>Max Depth = 16 + In Cycle
<LI>Call Chain = apply_theme &rArr;  apply_theme (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_theme
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_theme_recursion
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_theme
</UL>

<P><STRONG><a name="[c4]"></a>apply_theme_recursion</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, lv_theme.o(i.apply_theme_recursion))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + In Cycle
<LI>Call Chain = apply_theme_recursion &rArr;  apply_theme_recursion (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_theme_recursion
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_theme
</UL>
<BR>[Called By]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_theme_apply
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_theme_recursion
</UL>

<P><STRONG><a name="[107]"></a>draw_image</STRONG> (Thumb, 942 bytes, Stack size 168 bytes, lv_image.o(i.draw_image))
<BR><BR>[Stack]<UL><LI>Max Depth = 832<LI>Call Chain = draw_image &rArr; lv_draw_image &rArr; lv_draw_finalize_task_creation &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_style_prop
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_param
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_layer
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_current_target
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_in
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_width
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_get_height
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_label_dsc
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_init_draw_image_dsc
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_code
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_intersect
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_set
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image_dsc_init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_color_format_has_alpha
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_move
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_align
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_buf_get_transformed_area
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_image
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_text_get_size
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_label_dsc_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_draw_label
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_get_scale
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_get_pivot
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
</UL>

<P><STRONG><a name="[85]"></a>lv_image_constructor</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, lv_image.o(i.lv_image_constructor))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = lv_image_constructor &rArr; lv_obj_add_flag &rArr; lv_obj_get_scrollbar_area &rArr; lv_obj_get_scroll_right &rArr; lv_obj_get_self_width &rArr; lv_obj_send_event &rArr; event_send_core &rArr;  event_send_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_remove_flag
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_add_flag
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_point_set
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_image.o(.constdata)
</UL>
<P><STRONG><a name="[86]"></a>lv_image_destructor</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, lv_image.o(i.lv_image_destructor))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = lv_image_destructor &rArr; lv_free &rArr; lv_free_core &rArr; lv_tlsf_free &rArr; block_merge_prev &rArr; block_absorb &rArr; block_link_next &rArr; block_next &rArr; block_is_last
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_free
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_image.o(.constdata)
</UL>
<P><STRONG><a name="[87]"></a>lv_image_event</STRONG> (Thumb, 522 bytes, Stack size 64 bytes, lv_image.o(i.lv_image_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 904<LI>Call Chain = lv_image_event &rArr; lv_image_set_src &rArr; update_align &rArr; lv_image_set_pivot &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_param
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_current_target
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_event_base
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_event_get_code
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_area_is_point_on
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_click_area
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_buf_get_transformed_area
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_align
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_image
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_get_pivot
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lv_image.o(.constdata)
</UL>
<P><STRONG><a name="[24f]"></a>scale_update</STRONG> (Thumb, 240 bytes, Stack size 72 bytes, lv_image.o(i.scale_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 760<LI>Call Chain = scale_update &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_refresh_ext_draw_size
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_invalidate_area
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layout
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_display
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_display_enable_invalidation
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_buf_get_transformed_area
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_get_pivot
</UL>
<BR>[Called By]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_align
</UL>

<P><STRONG><a name="[1b8]"></a>update_align</STRONG> (Thumb, 308 bytes, Stack size 16 bytes, lv_image.o(i.update_align))
<BR><BR>[Stack]<UL><LI>Max Depth = 776<LI>Call Chain = update_align &rArr; lv_image_set_pivot &rArr; lv_obj_update_layout &rArr; layout_update_core &rArr;  layout_update_core (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_width
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_get_height
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_obj_update_layout
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_rotation
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_pivot
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scale_update
</UL>
<BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_set_src
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lv_image_event
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
