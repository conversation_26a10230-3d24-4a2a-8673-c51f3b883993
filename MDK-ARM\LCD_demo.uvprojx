<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>LCD_demo</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARM960</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F429IGTx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32F4xx_DFP.2.17.0</PackID>
          <PackURL>https://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000-0x2002FFFF) IRAM2(0x10000000-0x1000FFFF) IROM(0x8000000-0x80FFFFF)  CLOCK(25000000) FPU2 CPUTYPE("Cortex-M4") TZ</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile></RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F429IGTx$CMSIS\SVD\STM32F429.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>LCD_demo\</OutputDirectory>
          <OutputName>LCD_demo</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath></ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>0</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>-REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments>-MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4101</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2V8M.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>1</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x30000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x30000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>2</Optim>
            <oTime>1</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <useXO>0</useXO>
            <v6Lang>5</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>--diag_suppress=1</MiscControls>
              <Define>USE_HAL_DRIVER,STM32F429xx</Define>
              <Undefine></Undefine>
              <IncludePath>../Core/Inc;../Drivers/STM32F4xx_HAL_Driver/Inc;../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy;../Drivers/CMSIS/Device/ST/STM32F4xx/Include;../Drivers/CMSIS/Include;../GUI;../GUI/lvgl;../GUI/lvgl/src;../GUI/lvgl/examples/porting;../GUI/lvgl/examples;../my_Drivers;..\GUI\lvgl\generated</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange></TextAddressRange>
            <DataAddressRange></DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>LCD_demo\LCD_demo.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--remove</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Application/MDK-ARM</GroupName>
          <Files>
            <File>
              <FileName>startup_stm32f429xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>startup_stm32f429xx.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Application/User/Core</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/main.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/gpio.c</FilePath>
            </File>
            <File>
              <FileName>dma2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/dma2d.c</FilePath>
            </File>
            <File>
              <FileName>fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/fmc.c</FilePath>
            </File>
            <File>
              <FileName>ltdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/ltdc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/stm32f4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_msp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/stm32f4xx_hal_msp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/STM32F4xx_HAL_Driver</GroupName>
          <Files>
            <File>
              <FileName>stm32f4xx_hal_dma2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma2d.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_flash_ramfunc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_ll_fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_fmc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_nor.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_nor.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_sram.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sram.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_nand.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_nand.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_pccard.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pccard.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_sdram.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sdram.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_ltdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_ltdc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_ltdc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_ltdc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_hal_dsi.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dsi.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_stm32f4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Core/Src/system_stm32f4xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lvgl_porting</GroupName>
          <Files>
            <File>
              <FileName>lv_port_disp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\examples\porting\lv_port_disp.c</FilePath>
            </File>
            <File>
              <FileName>lv_port_indev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\examples\porting\lv_port_indev.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lvgl_app</GroupName>
          <Files>
            <File>
              <FileName>events_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\generated\events_init.c</FilePath>
            </File>
            <File>
              <FileName>gui_guider.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\generated\gui_guider.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_test1.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\generated\setup_scr_test1.c</FilePath>
            </File>
            <File>
              <FileName>widgets_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\generated\widgets_init.c</FilePath>
            </File>
            <File>
              <FileName>_test1_RGB565_480x800.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\generated\images\_test1_RGB565_480x800.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lvgl_src</GroupName>
          <Files>
            <File>
              <FileName>lv_group.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_group.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_obj.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_class.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_obj_class.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_draw.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_obj_draw.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_event.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_obj_event.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_id_builtin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_obj_id_builtin.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_pos.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_obj_pos.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_property.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_obj_property.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_scroll.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_obj_scroll.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_style.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_obj_style.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_style_gen.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_obj_style_gen.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_tree.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_obj_tree.c</FilePath>
            </File>
            <File>
              <FileName>lv_refr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\core\lv_refr.c</FilePath>
            </File>
            <File>
              <FileName>lv_display.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\display\lv_display.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dma2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\dma2d\lv_draw_dma2d.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dma2d_fill.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\dma2d\lv_draw_dma2d_fill.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dma2d_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\dma2d\lv_draw_dma2d_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_nema_gfx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_draw_nema_gfx.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_nema_gfx_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_draw_nema_gfx_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_nema_gfx_border.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_draw_nema_gfx_border.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_nema_gfx_fill.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_draw_nema_gfx_fill.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_nema_gfx_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_draw_nema_gfx_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_nema_gfx_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_draw_nema_gfx_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_nema_gfx_layer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_draw_nema_gfx_layer.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_nema_gfx_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_draw_nema_gfx_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_nema_gfx_stm32_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_draw_nema_gfx_stm32_hal.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_nema_gfx_triangle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_draw_nema_gfx_triangle.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_nema_gfx_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_draw_nema_gfx_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_nema_gfx_path.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nema_gfx\lv_nema_gfx_path.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_buf_g2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\g2d\lv_draw_buf_g2d.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_g2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\g2d\lv_draw_g2d.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_g2d_fill.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\g2d\lv_draw_g2d_fill.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_g2d_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\g2d\lv_draw_g2d_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_g2d_buf_map.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\g2d\lv_g2d_buf_map.c</FilePath>
            </File>
            <File>
              <FileName>lv_g2d_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\g2d\lv_g2d_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_buf_pxp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\pxp\lv_draw_buf_pxp.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_pxp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\pxp\lv_draw_pxp.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_pxp_fill.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\pxp\lv_draw_pxp_fill.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_pxp_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\pxp\lv_draw_pxp_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_pxp_layer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\pxp\lv_draw_pxp_layer.c</FilePath>
            </File>
            <File>
              <FileName>lv_pxp_cfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\pxp\lv_pxp_cfg.c</FilePath>
            </File>
            <File>
              <FileName>lv_pxp_osa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\pxp\lv_pxp_osa.c</FilePath>
            </File>
            <File>
              <FileName>lv_pxp_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\pxp\lv_pxp_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_buf_vglite.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_draw_buf_vglite.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_draw_vglite.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_draw_vglite_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_border.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_draw_vglite_border.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_fill.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_draw_vglite_fill.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_draw_vglite_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_draw_vglite_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_layer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_draw_vglite_layer.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_draw_vglite_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vglite_triangle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_draw_vglite_triangle.c</FilePath>
            </File>
            <File>
              <FileName>lv_vglite_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_vglite_buf.c</FilePath>
            </File>
            <File>
              <FileName>lv_vglite_matrix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_vglite_matrix.c</FilePath>
            </File>
            <File>
              <FileName>lv_vglite_path.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_vglite_path.c</FilePath>
            </File>
            <File>
              <FileName>lv_vglite_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\nxp\vglite\lv_vglite_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_opengles.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\opengles\lv_draw_opengles.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dave2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\renesas\dave2d\lv_draw_dave2d.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dave2d_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\renesas\dave2d\lv_draw_dave2d_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dave2d_border.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\renesas\dave2d\lv_draw_dave2d_border.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dave2d_fill.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\renesas\dave2d\lv_draw_dave2d_fill.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dave2d_image.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\renesas\dave2d\lv_draw_dave2d_image.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dave2d_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\renesas\dave2d\lv_draw_dave2d_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dave2d_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\renesas\dave2d\lv_draw_dave2d_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dave2d_mask_rectangle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\renesas\dave2d\lv_draw_dave2d_mask_rectangle.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dave2d_triangle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\renesas\dave2d\lv_draw_dave2d_triangle.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_dave2d_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\renesas\dave2d\lv_draw_dave2d_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sdl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sdl\lv_draw_sdl.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_blend.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\blend\lv_draw_sw_blend.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_blend_to_al88.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\blend\lv_draw_sw_blend_to_al88.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_blend_to_argb8888.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\blend\lv_draw_sw_blend_to_argb8888.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_blend_to_argb8888_premultiplied.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\blend\lv_draw_sw_blend_to_argb8888_premultiplied.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_blend_to_i1.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\blend\lv_draw_sw_blend_to_i1.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_blend_to_l8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\blend\lv_draw_sw_blend_to_l8.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_blend_to_rgb565.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\blend\lv_draw_sw_blend_to_rgb565.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_blend_to_rgb565_swapped.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\blend\lv_draw_sw_blend_to_rgb565_swapped.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_blend_to_rgb888.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\blend\lv_draw_sw_blend_to_rgb888.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_border.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_border.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_box_shadow.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_box_shadow.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_fill.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_fill.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_grad.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_grad.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_letter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_letter.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_mask.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_mask.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_mask_rect.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_mask_rect.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_transform.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_transform.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_triangle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_triangle.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_vector.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\sw\lv_draw_sw_vector.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_buf_vg_lite.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_buf_vg_lite.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite_border.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite_border.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite_box_shadow.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite_box_shadow.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite_fill.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite_fill.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite_layer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite_layer.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite_mask_rect.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite_mask_rect.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite_triangle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite_triangle.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vg_lite_vector.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_draw_vg_lite_vector.c</FilePath>
            </File>
            <File>
              <FileName>lv_vg_lite_decoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_vg_lite_decoder.c</FilePath>
            </File>
            <File>
              <FileName>lv_vg_lite_grad.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_vg_lite_grad.c</FilePath>
            </File>
            <File>
              <FileName>lv_vg_lite_math.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_vg_lite_math.c</FilePath>
            </File>
            <File>
              <FileName>lv_vg_lite_path.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_vg_lite_path.c</FilePath>
            </File>
            <File>
              <FileName>lv_vg_lite_pending.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_vg_lite_pending.c</FilePath>
            </File>
            <File>
              <FileName>lv_vg_lite_stroke.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_vg_lite_stroke.c</FilePath>
            </File>
            <File>
              <FileName>lv_vg_lite_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\vg_lite\lv_vg_lite_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_draw.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_3d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_draw_3d.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_draw_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_draw_buf.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_image.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_draw_image.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_draw_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_draw_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_mask.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_draw_mask.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_rect.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_draw_rect.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_triangle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_draw_triangle.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_vector.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_draw_vector.c</FilePath>
            </File>
            <File>
              <FileName>lv_image_decoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\draw\lv_image_decoder.c</FilePath>
            </File>
            <File>
              <FileName>lv_st_ltdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\drivers\display\st_ltdc\lv_st_ltdc.c</FilePath>
            </File>
            <File>
              <FileName>lv_binfont_loader.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_binfont_loader.c</FilePath>
            </File>
            <File>
              <FileName>lv_font.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_dejavu_16_persian_hebrew.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_dejavu_16_persian_hebrew.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_fmt_txt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_fmt_txt.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_8.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_10.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_10.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_12.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_14.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_14.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_14_aligned.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_14_aligned.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_16.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_18.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_18.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_20.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_22.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_22.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_24.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_24.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_26.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_26.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_28.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_28.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_28_compressed.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_28_compressed.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_30.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_30.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_32.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_34.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_34.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_36.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_36.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_38.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_38.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_40.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_40.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_42.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_42.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_44.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_44.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_46.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_46.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_48.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_montserrat_48.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_simsun_14_cjk.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_simsun_14_cjk.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_simsun_16_cjk.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_simsun_16_cjk.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_source_han_sans_sc_14_cjk.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_source_han_sans_sc_14_cjk.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_source_han_sans_sc_16_cjk.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_source_han_sans_sc_16_cjk.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_unscii_8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_unscii_8.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_unscii_16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\font\lv_font_unscii_16.c</FilePath>
            </File>
            <File>
              <FileName>lv_indev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\indev\lv_indev.c</FilePath>
            </File>
            <File>
              <FileName>lv_indev_gesture.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\indev\lv_indev_gesture.c</FilePath>
            </File>
            <File>
              <FileName>lv_indev_scroll.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\indev\lv_indev_scroll.c</FilePath>
            </File>
            <File>
              <FileName>lv_flex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\layouts\flex\lv_flex.c</FilePath>
            </File>
            <File>
              <FileName>lv_grid.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\layouts\grid\lv_grid.c</FilePath>
            </File>
            <File>
              <FileName>lv_layout.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\layouts\lv_layout.c</FilePath>
            </File>
            <File>
              <FileName>code128.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\barcode\code128.c</FilePath>
            </File>
            <File>
              <FileName>lv_barcode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\barcode\lv_barcode.c</FilePath>
            </File>
            <File>
              <FileName>lv_bin_decoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\bin_decoder\lv_bin_decoder.c</FilePath>
            </File>
            <File>
              <FileName>lv_bmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\bmp\lv_bmp.c</FilePath>
            </File>
            <File>
              <FileName>xmlparse.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\expat\xmlparse.c</FilePath>
            </File>
            <File>
              <FileName>xmlrole.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\expat\xmlrole.c</FilePath>
            </File>
            <File>
              <FileName>xmltok.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\expat\xmltok.c</FilePath>
            </File>
            <File>
              <FileName>xmltok_impl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\expat\xmltok_impl.c</FilePath>
            </File>
            <File>
              <FileName>xmltok_ns.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\expat\xmltok_ns.c</FilePath>
            </File>
            <File>
              <FileName>lv_ffmpeg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\ffmpeg\lv_ffmpeg.c</FilePath>
            </File>
            <File>
              <FileName>lv_freetype.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\freetype\lv_freetype.c</FilePath>
            </File>
            <File>
              <FileName>lv_freetype_glyph.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\freetype\lv_freetype_glyph.c</FilePath>
            </File>
            <File>
              <FileName>lv_freetype_image.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\freetype\lv_freetype_image.c</FilePath>
            </File>
            <File>
              <FileName>lv_freetype_outline.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\freetype\lv_freetype_outline.c</FilePath>
            </File>
            <File>
              <FileName>lv_ftsystem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\freetype\lv_ftsystem.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_cbfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\fsdrv\lv_fs_cbfs.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_fatfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\fsdrv\lv_fs_fatfs.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_littlefs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\fsdrv\lv_fs_littlefs.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_memfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\fsdrv\lv_fs_memfs.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_posix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\fsdrv\lv_fs_posix.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_stdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\fsdrv\lv_fs_stdio.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_uefi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\fsdrv\lv_fs_uefi.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_win32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\fsdrv\lv_fs_win32.c</FilePath>
            </File>
            <File>
              <FileName>gifdec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\gif\gifdec.c</FilePath>
            </File>
            <File>
              <FileName>lv_gif.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\gif\lv_gif.c</FilePath>
            </File>
            <File>
              <FileName>lv_libjpeg_turbo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\libjpeg_turbo\lv_libjpeg_turbo.c</FilePath>
            </File>
            <File>
              <FileName>lv_libpng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\libpng\lv_libpng.c</FilePath>
            </File>
            <File>
              <FileName>lodepng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\lodepng\lodepng.c</FilePath>
            </File>
            <File>
              <FileName>lv_lodepng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\lodepng\lv_lodepng.c</FilePath>
            </File>
            <File>
              <FileName>lz4.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\lz4\lz4.c</FilePath>
            </File>
            <File>
              <FileName>lv_qrcode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\qrcode\lv_qrcode.c</FilePath>
            </File>
            <File>
              <FileName>qrcodegen.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\qrcode\qrcodegen.c</FilePath>
            </File>
            <File>
              <FileName>lv_rle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\rle\lv_rle.c</FilePath>
            </File>
            <File>
              <FileName>lv_rlottie.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\rlottie\lv_rlottie.c</FilePath>
            </File>
            <File>
              <FileName>lv_svg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\svg\lv_svg.c</FilePath>
            </File>
            <File>
              <FileName>lv_svg_decoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\svg\lv_svg_decoder.c</FilePath>
            </File>
            <File>
              <FileName>lv_svg_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\svg\lv_svg_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_svg_render.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\svg\lv_svg_render.c</FilePath>
            </File>
            <File>
              <FileName>lv_svg_token.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\svg\lv_svg_token.c</FilePath>
            </File>
            <File>
              <FileName>lv_tiny_ttf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\tiny_ttf\lv_tiny_ttf.c</FilePath>
            </File>
            <File>
              <FileName>lv_tjpgd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\tjpgd\lv_tjpgd.c</FilePath>
            </File>
            <File>
              <FileName>tjpgd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\libs\tjpgd\tjpgd.c</FilePath>
            </File>
            <File>
              <FileName>lv_cache_lru_ll.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\cache\class\lv_cache_lru_ll.c</FilePath>
            </File>
            <File>
              <FileName>lv_cache_lru_rb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\cache\class\lv_cache_lru_rb.c</FilePath>
            </File>
            <File>
              <FileName>lv_image_cache.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\cache\instance\lv_image_cache.c</FilePath>
            </File>
            <File>
              <FileName>lv_image_header_cache.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\cache\instance\lv_image_header_cache.c</FilePath>
            </File>
            <File>
              <FileName>lv_cache.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\cache\lv_cache.c</FilePath>
            </File>
            <File>
              <FileName>lv_cache_entry.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\cache\lv_cache_entry.c</FilePath>
            </File>
            <File>
              <FileName>lv_anim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_anim.c</FilePath>
            </File>
            <File>
              <FileName>lv_anim_timeline.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_anim_timeline.c</FilePath>
            </File>
            <File>
              <FileName>lv_area.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_area.c</FilePath>
            </File>
            <File>
              <FileName>lv_array.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_array.c</FilePath>
            </File>
            <File>
              <FileName>lv_async.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_async.c</FilePath>
            </File>
            <File>
              <FileName>lv_bidi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_bidi.c</FilePath>
            </File>
            <File>
              <FileName>lv_circle_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_circle_buf.c</FilePath>
            </File>
            <File>
              <FileName>lv_color.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_color.c</FilePath>
            </File>
            <File>
              <FileName>lv_color_op.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_color_op.c</FilePath>
            </File>
            <File>
              <FileName>lv_event.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_event.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_fs.c</FilePath>
            </File>
            <File>
              <FileName>lv_grad.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_grad.c</FilePath>
            </File>
            <File>
              <FileName>lv_iter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_iter.c</FilePath>
            </File>
            <File>
              <FileName>lv_ll.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_ll.c</FilePath>
            </File>
            <File>
              <FileName>lv_log.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_log.c</FilePath>
            </File>
            <File>
              <FileName>lv_lru.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_lru.c</FilePath>
            </File>
            <File>
              <FileName>lv_math.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_math.c</FilePath>
            </File>
            <File>
              <FileName>lv_matrix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_matrix.c</FilePath>
            </File>
            <File>
              <FileName>lv_palette.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_palette.c</FilePath>
            </File>
            <File>
              <FileName>lv_profiler_builtin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_profiler_builtin.c</FilePath>
            </File>
            <File>
              <FileName>lv_rb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_rb.c</FilePath>
            </File>
            <File>
              <FileName>lv_style.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_style.c</FilePath>
            </File>
            <File>
              <FileName>lv_style_gen.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_style_gen.c</FilePath>
            </File>
            <File>
              <FileName>lv_templ.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_templ.c</FilePath>
            </File>
            <File>
              <FileName>lv_text.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_text.c</FilePath>
            </File>
            <File>
              <FileName>lv_text_ap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_text_ap.c</FilePath>
            </File>
            <File>
              <FileName>lv_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_timer.c</FilePath>
            </File>
            <File>
              <FileName>lv_tree.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_tree.c</FilePath>
            </File>
            <File>
              <FileName>lv_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\misc\lv_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_cmsis_rtos2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\osal\lv_cmsis_rtos2.c</FilePath>
            </File>
            <File>
              <FileName>lv_freertos.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\osal\lv_freertos.c</FilePath>
            </File>
            <File>
              <FileName>lv_linux.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\osal\lv_linux.c</FilePath>
            </File>
            <File>
              <FileName>lv_mqx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\osal\lv_mqx.c</FilePath>
            </File>
            <File>
              <FileName>lv_os.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\osal\lv_os.c</FilePath>
            </File>
            <File>
              <FileName>lv_os_none.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\osal\lv_os_none.c</FilePath>
            </File>
            <File>
              <FileName>lv_pthread.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\osal\lv_pthread.c</FilePath>
            </File>
            <File>
              <FileName>lv_rtthread.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\osal\lv_rtthread.c</FilePath>
            </File>
            <File>
              <FileName>lv_sdl2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\osal\lv_sdl2.c</FilePath>
            </File>
            <File>
              <FileName>lv_windows.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\osal\lv_windows.c</FilePath>
            </File>
            <File>
              <FileName>lv_file_explorer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\file_explorer\lv_file_explorer.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_manager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\font_manager\lv_font_manager.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_manager_recycle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\font_manager\lv_font_manager_recycle.c</FilePath>
            </File>
            <File>
              <FileName>lv_fragment.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\fragment\lv_fragment.c</FilePath>
            </File>
            <File>
              <FileName>lv_fragment_manager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\fragment\lv_fragment_manager.c</FilePath>
            </File>
            <File>
              <FileName>lv_gridnav.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\gridnav\lv_gridnav.c</FilePath>
            </File>
            <File>
              <FileName>lv_ime_pinyin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\ime\lv_ime_pinyin.c</FilePath>
            </File>
            <File>
              <FileName>lv_imgfont.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\imgfont\lv_imgfont.c</FilePath>
            </File>
            <File>
              <FileName>lv_monkey.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\monkey\lv_monkey.c</FilePath>
            </File>
            <File>
              <FileName>lv_observer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\observer\lv_observer.c</FilePath>
            </File>
            <File>
              <FileName>lv_snapshot.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\snapshot\lv_snapshot.c</FilePath>
            </File>
            <File>
              <FileName>lv_sysmon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\sysmon\lv_sysmon.c</FilePath>
            </File>
            <File>
              <FileName>lv_test_display.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\test\lv_test_display.c</FilePath>
            </File>
            <File>
              <FileName>lv_test_helpers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\test\lv_test_helpers.c</FilePath>
            </File>
            <File>
              <FileName>lv_test_indev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\test\lv_test_indev.c</FilePath>
            </File>
            <File>
              <FileName>lv_test_indev_gesture.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\test\lv_test_indev_gesture.c</FilePath>
            </File>
            <File>
              <FileName>lv_test_screenshot_compare.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\test\lv_test_screenshot_compare.c</FilePath>
            </File>
            <File>
              <FileName>vg_lite_matrix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\vg_lite_tvg\vg_lite_matrix.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_arc_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_arc_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_bar_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_bar_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_button_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_button_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_buttonmatrix_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_buttonmatrix_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_calendar_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_calendar_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_canvas_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_canvas_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_chart_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_chart_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_checkbox_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_checkbox_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_dropdown_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_dropdown_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_event_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_event_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_image_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_image_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_keyboard_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_keyboard_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_label_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_label_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_obj_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_obj_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_roller_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_roller_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_scale_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_scale_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_slider_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_slider_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_spangroup_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_spangroup_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_table_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_table_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_tabview_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_tabview_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_textarea_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\parsers\lv_xml_textarea_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\lv_xml.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_base_types.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\lv_xml_base_types.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_component.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\lv_xml_component.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\lv_xml_parser.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_style.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\lv_xml_style.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_update.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\lv_xml_update.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\lv_xml_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_xml_widget.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\others\xml\lv_xml_widget.c</FilePath>
            </File>
            <File>
              <FileName>lv_mem_core_clib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\stdlib\clib\lv_mem_core_clib.c</FilePath>
            </File>
            <File>
              <FileName>lv_sprintf_clib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\stdlib\clib\lv_sprintf_clib.c</FilePath>
            </File>
            <File>
              <FileName>lv_string_clib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\stdlib\clib\lv_string_clib.c</FilePath>
            </File>
            <File>
              <FileName>lv_mem_core_builtin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\stdlib\builtin\lv_mem_core_builtin.c</FilePath>
            </File>
            <File>
              <FileName>lv_sprintf_builtin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\stdlib\builtin\lv_sprintf_builtin.c</FilePath>
            </File>
            <File>
              <FileName>lv_string_builtin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\stdlib\builtin\lv_string_builtin.c</FilePath>
            </File>
            <File>
              <FileName>lv_tlsf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\stdlib\builtin\lv_tlsf.c</FilePath>
            </File>
            <File>
              <FileName>lv_mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\stdlib\lv_mem.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_default.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\themes\default\lv_theme_default.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_mono.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\themes\mono\lv_theme_mono.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_simple.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\themes\simple\lv_theme_simple.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\themes\lv_theme.c</FilePath>
            </File>
            <File>
              <FileName>lv_tick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\tick\lv_tick.c</FilePath>
            </File>
            <File>
              <FileName>lv_win.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\win\lv_win.c</FilePath>
            </File>
            <File>
              <FileName>lv_tileview.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\tileview\lv_tileview.c</FilePath>
            </File>
            <File>
              <FileName>lv_textarea.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\textarea\lv_textarea.c</FilePath>
            </File>
            <File>
              <FileName>lv_tabview.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\tabview\lv_tabview.c</FilePath>
            </File>
            <File>
              <FileName>lv_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\table\lv_table.c</FilePath>
            </File>
            <File>
              <FileName>lv_switch.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\switch\lv_switch.c</FilePath>
            </File>
            <File>
              <FileName>lv_spinner.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\spinner\lv_spinner.c</FilePath>
            </File>
            <File>
              <FileName>lv_spinbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\spinbox\lv_spinbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_span.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\span\lv_span.c</FilePath>
            </File>
            <File>
              <FileName>lv_slider.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\slider\lv_slider.c</FilePath>
            </File>
            <File>
              <FileName>lv_scale.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\scale\lv_scale.c</FilePath>
            </File>
            <File>
              <FileName>lv_roller.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\roller\lv_roller.c</FilePath>
            </File>
            <File>
              <FileName>lv_animimage_properties.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\property\lv_animimage_properties.c</FilePath>
            </File>
            <File>
              <FileName>lv_dropdown_properties.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\property\lv_dropdown_properties.c</FilePath>
            </File>
            <File>
              <FileName>lv_image_properties.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\property\lv_image_properties.c</FilePath>
            </File>
            <File>
              <FileName>lv_keyboard_properties.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\property\lv_keyboard_properties.c</FilePath>
            </File>
            <File>
              <FileName>lv_label_properties.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\property\lv_label_properties.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_properties.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\property\lv_obj_properties.c</FilePath>
            </File>
            <File>
              <FileName>lv_roller_properties.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\property\lv_roller_properties.c</FilePath>
            </File>
            <File>
              <FileName>lv_slider_properties.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\property\lv_slider_properties.c</FilePath>
            </File>
            <File>
              <FileName>lv_style_properties.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\property\lv_style_properties.c</FilePath>
            </File>
            <File>
              <FileName>lv_textarea_properties.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\property\lv_textarea_properties.c</FilePath>
            </File>
            <File>
              <FileName>lv_objx_templ.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\objx_templ\lv_objx_templ.c</FilePath>
            </File>
            <File>
              <FileName>lv_msgbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\msgbox\lv_msgbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_menu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\menu\lv_menu.c</FilePath>
            </File>
            <File>
              <FileName>lv_lottie.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\lottie\lv_lottie.c</FilePath>
            </File>
            <File>
              <FileName>lv_list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\list\lv_list.c</FilePath>
            </File>
            <File>
              <FileName>lv_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\line\lv_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\led\lv_led.c</FilePath>
            </File>
            <File>
              <FileName>lv_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\label\lv_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_keyboard.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\keyboard\lv_keyboard.c</FilePath>
            </File>
            <File>
              <FileName>lv_imagebutton.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\imagebutton\lv_imagebutton.c</FilePath>
            </File>
            <File>
              <FileName>lv_image.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\image\lv_image.c</FilePath>
            </File>
            <File>
              <FileName>lv_checkbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\checkbox\lv_checkbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_dropdown.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\dropdown\lv_dropdown.c</FilePath>
            </File>
            <File>
              <FileName>lv_chart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\chart\lv_chart.c</FilePath>
            </File>
            <File>
              <FileName>lv_canvas.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\canvas\lv_canvas.c</FilePath>
            </File>
            <File>
              <FileName>lv_calendar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\calendar\lv_calendar.c</FilePath>
            </File>
            <File>
              <FileName>lv_calendar_chinese.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\calendar\lv_calendar_chinese.c</FilePath>
            </File>
            <File>
              <FileName>lv_calendar_header_arrow.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\calendar\lv_calendar_header_arrow.c</FilePath>
            </File>
            <File>
              <FileName>lv_calendar_header_dropdown.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\calendar\lv_calendar_header_dropdown.c</FilePath>
            </File>
            <File>
              <FileName>lv_buttonmatrix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\buttonmatrix\lv_buttonmatrix.c</FilePath>
            </File>
            <File>
              <FileName>lv_button.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\button\lv_button.c</FilePath>
            </File>
            <File>
              <FileName>lv_bar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\bar\lv_bar.c</FilePath>
            </File>
            <File>
              <FileName>lv_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\arc\lv_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_animimage.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\animimage\lv_animimage.c</FilePath>
            </File>
            <File>
              <FileName>lv_3dtexture.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\widgets\3dtexture\lv_3dtexture.c</FilePath>
            </File>
            <File>
              <FileName>lv_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\GUI\lvgl\src\lv_init.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>my_drivers</GroupName>
          <Files>
            <File>
              <FileName>my_ltdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\my_Drivers\my_ltdc.c</FilePath>
            </File>
            <File>
              <FileName>my_sdram.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\my_Drivers\my_sdram.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="4.3.0" condition="CMSIS Core">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="4.5.0"/>
        <targetInfos>
          <targetInfo name="LCD_demo"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

</Project>
