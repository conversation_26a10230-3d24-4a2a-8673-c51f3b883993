--cpu=Cortex-M4.fp
"lcd_demo\startup_stm32f429xx.o"
"lcd_demo\main.o"
"lcd_demo\gpio.o"
"lcd_demo\dma2d.o"
"lcd_demo\fmc.o"
"lcd_demo\ltdc.o"
"lcd_demo\stm32f4xx_it.o"
"lcd_demo\stm32f4xx_hal_msp.o"
"lcd_demo\stm32f4xx_hal_dma2d.o"
"lcd_demo\stm32f4xx_hal_rcc.o"
"lcd_demo\stm32f4xx_hal_rcc_ex.o"
"lcd_demo\stm32f4xx_hal_flash.o"
"lcd_demo\stm32f4xx_hal_flash_ex.o"
"lcd_demo\stm32f4xx_hal_flash_ramfunc.o"
"lcd_demo\stm32f4xx_hal_gpio.o"
"lcd_demo\stm32f4xx_hal_dma_ex.o"
"lcd_demo\stm32f4xx_hal_dma.o"
"lcd_demo\stm32f4xx_hal_pwr.o"
"lcd_demo\stm32f4xx_hal_pwr_ex.o"
"lcd_demo\stm32f4xx_hal_cortex.o"
"lcd_demo\stm32f4xx_hal.o"
"lcd_demo\stm32f4xx_hal_exti.o"
"lcd_demo\stm32f4xx_ll_fmc.o"
"lcd_demo\stm32f4xx_hal_nor.o"
"lcd_demo\stm32f4xx_hal_sram.o"
"lcd_demo\stm32f4xx_hal_nand.o"
"lcd_demo\stm32f4xx_hal_pccard.o"
"lcd_demo\stm32f4xx_hal_sdram.o"
"lcd_demo\stm32f4xx_hal_ltdc.o"
"lcd_demo\stm32f4xx_hal_ltdc_ex.o"
"lcd_demo\stm32f4xx_hal_dsi.o"
"lcd_demo\system_stm32f4xx.o"
"lcd_demo\lv_port_disp.o"
"lcd_demo\lv_port_indev.o"
"lcd_demo\events_init.o"
"lcd_demo\gui_guider.o"
"lcd_demo\setup_scr_test1.o"
"lcd_demo\widgets_init.o"
"lcd_demo\_test1_rgb565_480x800.o"
"lcd_demo\lv_group.o"
"lcd_demo\lv_obj.o"
"lcd_demo\lv_obj_class.o"
"lcd_demo\lv_obj_draw.o"
"lcd_demo\lv_obj_event.o"
"lcd_demo\lv_obj_id_builtin.o"
"lcd_demo\lv_obj_pos.o"
"lcd_demo\lv_obj_property.o"
"lcd_demo\lv_obj_scroll.o"
"lcd_demo\lv_obj_style.o"
"lcd_demo\lv_obj_style_gen.o"
"lcd_demo\lv_obj_tree.o"
"lcd_demo\lv_refr.o"
"lcd_demo\lv_display.o"
"lcd_demo\lv_draw_dma2d.o"
"lcd_demo\lv_draw_dma2d_fill.o"
"lcd_demo\lv_draw_dma2d_img.o"
"lcd_demo\lv_draw_nema_gfx.o"
"lcd_demo\lv_draw_nema_gfx_arc.o"
"lcd_demo\lv_draw_nema_gfx_border.o"
"lcd_demo\lv_draw_nema_gfx_fill.o"
"lcd_demo\lv_draw_nema_gfx_img.o"
"lcd_demo\lv_draw_nema_gfx_label.o"
"lcd_demo\lv_draw_nema_gfx_layer.o"
"lcd_demo\lv_draw_nema_gfx_line.o"
"lcd_demo\lv_draw_nema_gfx_stm32_hal.o"
"lcd_demo\lv_draw_nema_gfx_triangle.o"
"lcd_demo\lv_draw_nema_gfx_utils.o"
"lcd_demo\lv_nema_gfx_path.o"
"lcd_demo\lv_draw_buf_g2d.o"
"lcd_demo\lv_draw_g2d.o"
"lcd_demo\lv_draw_g2d_fill.o"
"lcd_demo\lv_draw_g2d_img.o"
"lcd_demo\lv_g2d_buf_map.o"
"lcd_demo\lv_g2d_utils.o"
"lcd_demo\lv_draw_buf_pxp.o"
"lcd_demo\lv_draw_pxp.o"
"lcd_demo\lv_draw_pxp_fill.o"
"lcd_demo\lv_draw_pxp_img.o"
"lcd_demo\lv_draw_pxp_layer.o"
"lcd_demo\lv_pxp_cfg.o"
"lcd_demo\lv_pxp_osa.o"
"lcd_demo\lv_pxp_utils.o"
"lcd_demo\lv_draw_buf_vglite.o"
"lcd_demo\lv_draw_vglite.o"
"lcd_demo\lv_draw_vglite_arc.o"
"lcd_demo\lv_draw_vglite_border.o"
"lcd_demo\lv_draw_vglite_fill.o"
"lcd_demo\lv_draw_vglite_img.o"
"lcd_demo\lv_draw_vglite_label.o"
"lcd_demo\lv_draw_vglite_layer.o"
"lcd_demo\lv_draw_vglite_line.o"
"lcd_demo\lv_draw_vglite_triangle.o"
"lcd_demo\lv_vglite_buf.o"
"lcd_demo\lv_vglite_matrix.o"
"lcd_demo\lv_vglite_path.o"
"lcd_demo\lv_vglite_utils.o"
"lcd_demo\lv_draw_opengles.o"
"lcd_demo\lv_draw_dave2d.o"
"lcd_demo\lv_draw_dave2d_arc.o"
"lcd_demo\lv_draw_dave2d_border.o"
"lcd_demo\lv_draw_dave2d_fill.o"
"lcd_demo\lv_draw_dave2d_image.o"
"lcd_demo\lv_draw_dave2d_label.o"
"lcd_demo\lv_draw_dave2d_line.o"
"lcd_demo\lv_draw_dave2d_mask_rectangle.o"
"lcd_demo\lv_draw_dave2d_triangle.o"
"lcd_demo\lv_draw_dave2d_utils.o"
"lcd_demo\lv_draw_sdl.o"
"lcd_demo\lv_draw_sw_blend.o"
"lcd_demo\lv_draw_sw_blend_to_al88.o"
"lcd_demo\lv_draw_sw_blend_to_argb8888.o"
"lcd_demo\lv_draw_sw_blend_to_argb8888_premultiplied.o"
"lcd_demo\lv_draw_sw_blend_to_i1.o"
"lcd_demo\lv_draw_sw_blend_to_l8.o"
"lcd_demo\lv_draw_sw_blend_to_rgb565.o"
"lcd_demo\lv_draw_sw_blend_to_rgb565_swapped.o"
"lcd_demo\lv_draw_sw_blend_to_rgb888.o"
"lcd_demo\lv_draw_sw.o"
"lcd_demo\lv_draw_sw_arc.o"
"lcd_demo\lv_draw_sw_border.o"
"lcd_demo\lv_draw_sw_box_shadow.o"
"lcd_demo\lv_draw_sw_fill.o"
"lcd_demo\lv_draw_sw_grad.o"
"lcd_demo\lv_draw_sw_img.o"
"lcd_demo\lv_draw_sw_letter.o"
"lcd_demo\lv_draw_sw_line.o"
"lcd_demo\lv_draw_sw_mask.o"
"lcd_demo\lv_draw_sw_mask_rect.o"
"lcd_demo\lv_draw_sw_transform.o"
"lcd_demo\lv_draw_sw_triangle.o"
"lcd_demo\lv_draw_sw_utils.o"
"lcd_demo\lv_draw_sw_vector.o"
"lcd_demo\lv_draw_buf_vg_lite.o"
"lcd_demo\lv_draw_vg_lite.o"
"lcd_demo\lv_draw_vg_lite_arc.o"
"lcd_demo\lv_draw_vg_lite_border.o"
"lcd_demo\lv_draw_vg_lite_box_shadow.o"
"lcd_demo\lv_draw_vg_lite_fill.o"
"lcd_demo\lv_draw_vg_lite_img.o"
"lcd_demo\lv_draw_vg_lite_label.o"
"lcd_demo\lv_draw_vg_lite_layer.o"
"lcd_demo\lv_draw_vg_lite_line.o"
"lcd_demo\lv_draw_vg_lite_mask_rect.o"
"lcd_demo\lv_draw_vg_lite_triangle.o"
"lcd_demo\lv_draw_vg_lite_vector.o"
"lcd_demo\lv_vg_lite_decoder.o"
"lcd_demo\lv_vg_lite_grad.o"
"lcd_demo\lv_vg_lite_math.o"
"lcd_demo\lv_vg_lite_path.o"
"lcd_demo\lv_vg_lite_pending.o"
"lcd_demo\lv_vg_lite_stroke.o"
"lcd_demo\lv_vg_lite_utils.o"
"lcd_demo\lv_draw.o"
"lcd_demo\lv_draw_3d.o"
"lcd_demo\lv_draw_arc.o"
"lcd_demo\lv_draw_buf.o"
"lcd_demo\lv_draw_image.o"
"lcd_demo\lv_draw_label.o"
"lcd_demo\lv_draw_line.o"
"lcd_demo\lv_draw_mask.o"
"lcd_demo\lv_draw_rect.o"
"lcd_demo\lv_draw_triangle.o"
"lcd_demo\lv_draw_vector.o"
"lcd_demo\lv_image_decoder.o"
"lcd_demo\lv_st_ltdc.o"
"lcd_demo\lv_binfont_loader.o"
"lcd_demo\lv_font.o"
"lcd_demo\lv_font_dejavu_16_persian_hebrew.o"
"lcd_demo\lv_font_fmt_txt.o"
"lcd_demo\lv_font_montserrat_8.o"
"lcd_demo\lv_font_montserrat_10.o"
"lcd_demo\lv_font_montserrat_12.o"
"lcd_demo\lv_font_montserrat_14.o"
"lcd_demo\lv_font_montserrat_14_aligned.o"
"lcd_demo\lv_font_montserrat_16.o"
"lcd_demo\lv_font_montserrat_18.o"
"lcd_demo\lv_font_montserrat_20.o"
"lcd_demo\lv_font_montserrat_22.o"
"lcd_demo\lv_font_montserrat_24.o"
"lcd_demo\lv_font_montserrat_26.o"
"lcd_demo\lv_font_montserrat_28.o"
"lcd_demo\lv_font_montserrat_28_compressed.o"
"lcd_demo\lv_font_montserrat_30.o"
"lcd_demo\lv_font_montserrat_32.o"
"lcd_demo\lv_font_montserrat_34.o"
"lcd_demo\lv_font_montserrat_36.o"
"lcd_demo\lv_font_montserrat_38.o"
"lcd_demo\lv_font_montserrat_40.o"
"lcd_demo\lv_font_montserrat_42.o"
"lcd_demo\lv_font_montserrat_44.o"
"lcd_demo\lv_font_montserrat_46.o"
"lcd_demo\lv_font_montserrat_48.o"
"lcd_demo\lv_font_simsun_14_cjk.o"
"lcd_demo\lv_font_simsun_16_cjk.o"
"lcd_demo\lv_font_source_han_sans_sc_14_cjk.o"
"lcd_demo\lv_font_source_han_sans_sc_16_cjk.o"
"lcd_demo\lv_font_unscii_8.o"
"lcd_demo\lv_font_unscii_16.o"
"lcd_demo\lv_indev.o"
"lcd_demo\lv_indev_gesture.o"
"lcd_demo\lv_indev_scroll.o"
"lcd_demo\lv_flex.o"
"lcd_demo\lv_grid.o"
"lcd_demo\lv_layout.o"
"lcd_demo\code128.o"
"lcd_demo\lv_barcode.o"
"lcd_demo\lv_bin_decoder.o"
"lcd_demo\lv_bmp.o"
"lcd_demo\xmlparse.o"
"lcd_demo\xmlrole.o"
"lcd_demo\xmltok.o"
"lcd_demo\xmltok_impl.o"
"lcd_demo\xmltok_ns.o"
"lcd_demo\lv_ffmpeg.o"
"lcd_demo\lv_freetype.o"
"lcd_demo\lv_freetype_glyph.o"
"lcd_demo\lv_freetype_image.o"
"lcd_demo\lv_freetype_outline.o"
"lcd_demo\lv_ftsystem.o"
"lcd_demo\lv_fs_cbfs.o"
"lcd_demo\lv_fs_fatfs.o"
"lcd_demo\lv_fs_littlefs.o"
"lcd_demo\lv_fs_memfs.o"
"lcd_demo\lv_fs_posix.o"
"lcd_demo\lv_fs_stdio.o"
"lcd_demo\lv_fs_uefi.o"
"lcd_demo\lv_fs_win32.o"
"lcd_demo\gifdec.o"
"lcd_demo\lv_gif.o"
"lcd_demo\lv_libjpeg_turbo.o"
"lcd_demo\lv_libpng.o"
"lcd_demo\lodepng.o"
"lcd_demo\lv_lodepng.o"
"lcd_demo\lz4.o"
"lcd_demo\lv_qrcode.o"
"lcd_demo\qrcodegen.o"
"lcd_demo\lv_rle.o"
"lcd_demo\lv_rlottie.o"
"lcd_demo\lv_svg.o"
"lcd_demo\lv_svg_decoder.o"
"lcd_demo\lv_svg_parser.o"
"lcd_demo\lv_svg_render.o"
"lcd_demo\lv_svg_token.o"
"lcd_demo\lv_tiny_ttf.o"
"lcd_demo\lv_tjpgd.o"
"lcd_demo\tjpgd.o"
"lcd_demo\lv_cache_lru_ll.o"
"lcd_demo\lv_cache_lru_rb.o"
"lcd_demo\lv_image_cache.o"
"lcd_demo\lv_image_header_cache.o"
"lcd_demo\lv_cache.o"
"lcd_demo\lv_cache_entry.o"
"lcd_demo\lv_anim.o"
"lcd_demo\lv_anim_timeline.o"
"lcd_demo\lv_area.o"
"lcd_demo\lv_array.o"
"lcd_demo\lv_async.o"
"lcd_demo\lv_bidi.o"
"lcd_demo\lv_circle_buf.o"
"lcd_demo\lv_color.o"
"lcd_demo\lv_color_op.o"
"lcd_demo\lv_event.o"
"lcd_demo\lv_fs.o"
"lcd_demo\lv_grad.o"
"lcd_demo\lv_iter.o"
"lcd_demo\lv_ll.o"
"lcd_demo\lv_log.o"
"lcd_demo\lv_lru.o"
"lcd_demo\lv_math.o"
"lcd_demo\lv_matrix.o"
"lcd_demo\lv_palette.o"
"lcd_demo\lv_profiler_builtin.o"
"lcd_demo\lv_rb.o"
"lcd_demo\lv_style.o"
"lcd_demo\lv_style_gen.o"
"lcd_demo\lv_templ.o"
"lcd_demo\lv_text.o"
"lcd_demo\lv_text_ap.o"
"lcd_demo\lv_timer.o"
"lcd_demo\lv_tree.o"
"lcd_demo\lv_utils.o"
"lcd_demo\lv_cmsis_rtos2.o"
"lcd_demo\lv_freertos.o"
"lcd_demo\lv_linux.o"
"lcd_demo\lv_mqx.o"
"lcd_demo\lv_os.o"
"lcd_demo\lv_os_none.o"
"lcd_demo\lv_pthread.o"
"lcd_demo\lv_rtthread.o"
"lcd_demo\lv_sdl2.o"
"lcd_demo\lv_windows.o"
"lcd_demo\lv_file_explorer.o"
"lcd_demo\lv_font_manager.o"
"lcd_demo\lv_font_manager_recycle.o"
"lcd_demo\lv_fragment.o"
"lcd_demo\lv_fragment_manager.o"
"lcd_demo\lv_gridnav.o"
"lcd_demo\lv_ime_pinyin.o"
"lcd_demo\lv_imgfont.o"
"lcd_demo\lv_monkey.o"
"lcd_demo\lv_observer.o"
"lcd_demo\lv_snapshot.o"
"lcd_demo\lv_sysmon.o"
"lcd_demo\lv_test_display.o"
"lcd_demo\lv_test_helpers.o"
"lcd_demo\lv_test_indev.o"
"lcd_demo\lv_test_indev_gesture.o"
"lcd_demo\lv_test_screenshot_compare.o"
"lcd_demo\vg_lite_matrix.o"
"lcd_demo\lv_xml_arc_parser.o"
"lcd_demo\lv_xml_bar_parser.o"
"lcd_demo\lv_xml_button_parser.o"
"lcd_demo\lv_xml_buttonmatrix_parser.o"
"lcd_demo\lv_xml_calendar_parser.o"
"lcd_demo\lv_xml_canvas_parser.o"
"lcd_demo\lv_xml_chart_parser.o"
"lcd_demo\lv_xml_checkbox_parser.o"
"lcd_demo\lv_xml_dropdown_parser.o"
"lcd_demo\lv_xml_event_parser.o"
"lcd_demo\lv_xml_image_parser.o"
"lcd_demo\lv_xml_keyboard_parser.o"
"lcd_demo\lv_xml_label_parser.o"
"lcd_demo\lv_xml_obj_parser.o"
"lcd_demo\lv_xml_roller_parser.o"
"lcd_demo\lv_xml_scale_parser.o"
"lcd_demo\lv_xml_slider_parser.o"
"lcd_demo\lv_xml_spangroup_parser.o"
"lcd_demo\lv_xml_table_parser.o"
"lcd_demo\lv_xml_tabview_parser.o"
"lcd_demo\lv_xml_textarea_parser.o"
"lcd_demo\lv_xml.o"
"lcd_demo\lv_xml_base_types.o"
"lcd_demo\lv_xml_component.o"
"lcd_demo\lv_xml_parser.o"
"lcd_demo\lv_xml_style.o"
"lcd_demo\lv_xml_update.o"
"lcd_demo\lv_xml_utils.o"
"lcd_demo\lv_xml_widget.o"
"lcd_demo\lv_mem_core_clib.o"
"lcd_demo\lv_sprintf_clib.o"
"lcd_demo\lv_string_clib.o"
"lcd_demo\lv_mem_core_builtin.o"
"lcd_demo\lv_sprintf_builtin.o"
"lcd_demo\lv_string_builtin.o"
"lcd_demo\lv_tlsf.o"
"lcd_demo\lv_mem.o"
"lcd_demo\lv_theme_default.o"
"lcd_demo\lv_theme_mono.o"
"lcd_demo\lv_theme_simple.o"
"lcd_demo\lv_theme.o"
"lcd_demo\lv_tick.o"
"lcd_demo\lv_win.o"
"lcd_demo\lv_tileview.o"
"lcd_demo\lv_textarea.o"
"lcd_demo\lv_tabview.o"
"lcd_demo\lv_table.o"
"lcd_demo\lv_switch.o"
"lcd_demo\lv_spinner.o"
"lcd_demo\lv_spinbox.o"
"lcd_demo\lv_span.o"
"lcd_demo\lv_slider.o"
"lcd_demo\lv_scale.o"
"lcd_demo\lv_roller.o"
"lcd_demo\lv_animimage_properties.o"
"lcd_demo\lv_dropdown_properties.o"
"lcd_demo\lv_image_properties.o"
"lcd_demo\lv_keyboard_properties.o"
"lcd_demo\lv_label_properties.o"
"lcd_demo\lv_obj_properties.o"
"lcd_demo\lv_roller_properties.o"
"lcd_demo\lv_slider_properties.o"
"lcd_demo\lv_style_properties.o"
"lcd_demo\lv_textarea_properties.o"
"lcd_demo\lv_objx_templ.o"
"lcd_demo\lv_msgbox.o"
"lcd_demo\lv_menu.o"
"lcd_demo\lv_lottie.o"
"lcd_demo\lv_list.o"
"lcd_demo\lv_line.o"
"lcd_demo\lv_led.o"
"lcd_demo\lv_label.o"
"lcd_demo\lv_keyboard.o"
"lcd_demo\lv_imagebutton.o"
"lcd_demo\lv_image.o"
"lcd_demo\lv_checkbox.o"
"lcd_demo\lv_dropdown.o"
"lcd_demo\lv_chart.o"
"lcd_demo\lv_canvas.o"
"lcd_demo\lv_calendar.o"
"lcd_demo\lv_calendar_chinese.o"
"lcd_demo\lv_calendar_header_arrow.o"
"lcd_demo\lv_calendar_header_dropdown.o"
"lcd_demo\lv_buttonmatrix.o"
"lcd_demo\lv_button.o"
"lcd_demo\lv_bar.o"
"lcd_demo\lv_arc.o"
"lcd_demo\lv_animimage.o"
"lcd_demo\lv_3dtexture.o"
"lcd_demo\lv_init.o"
"lcd_demo\my_ltdc.o"
"lcd_demo\my_sdram.o"
--library_type=microlib --strict --scatter "LCD_demo\LCD_demo.sct"
--remove --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "LCD_demo.map" -o LCD_demo\LCD_demo.axf